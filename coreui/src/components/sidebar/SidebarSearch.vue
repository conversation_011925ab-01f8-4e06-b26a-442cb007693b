<template>
  <div class="sidebar-search-container" v-if="!minimize">
    <div class="search-wrapper">
      <c-icon name="cil-magnifying-glass" class="search-icon"/>
      <input
        type="text"
        class="sidebar-search-input"
        placeholder="Search menu..."
        v-model="searchValue"
        @input="onSearchInput"
      />
      <c-icon
        v-if="searchValue"
        name="cil-x"
        class="clear-icon"
        @click.native="clearSearch"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'SidebarSearch',
  props: {
    minimize: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchValue: this.value
    };
  },
  methods: {
    onSearchInput() {
      this.$emit('input', this.searchValue);
    },
    clearSearch() {
      this.searchValue = '';
      this.$emit('input', '');
    }
  },
  watch: {
    value(newVal) {
      this.searchValue = newVal;
    }
  }
}
</script>

<style scoped>
.sidebar-search-container {
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.sidebar-search-input {
  width: 100%;
  padding: 8px 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
}

.sidebar-search-input:focus {
  outline: none;
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.sidebar-search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.search-icon {
  position: absolute;
  left: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1rem;
}

.clear-icon {
  position: absolute;
  right: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.clear-icon:hover {
  color: #fff;
}
</style>
