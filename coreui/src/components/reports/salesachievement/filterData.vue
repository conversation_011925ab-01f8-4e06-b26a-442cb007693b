<template>
  <c-card>
    <c-card-header>{{ reportName }}</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input label="From Date" type="date" placeholder="Date" v-model="from_date" :max="dateLimit"></c-input>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input label="To Date" type="date" placeholder="Date" v-model="to_date" :max="dateLimit"
                   @input="getAllData"></c-input>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Line</template>
            <template #input>
              <input label="All" v-if="lines.length != 0" class="m-1" id="lines" type="checkbox" v-model="checkAllLines"
                     title="Check All Lines" @change="checkAllLine"/>
              <label for="lines" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Line" class="mt-2" @input="getLineData" multiple/>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label>
              Filter By <span style="color: red">*</span>
            </template>
            <template #input>
              <v-select v-model="filter" :options="filters" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Filter" class="mt-2"
                        @input="filter == 1 ? (user_id = []) : (division_id = [])"/>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-if="filter == 1">
          <c-form-group>
            <template #label> Division</template>
            <template #input>
              <input label="All" id="division" v-if="divisions.length != 0" class="m-1" type="checkbox"
                     v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs"/>
              <label for="division" v-if="divisions.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                        :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" multiple/>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8" v-if="filter == 2">
          <c-form-group>
            <template #label> Employee</template>
            <template #input>
              <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox" v-model="checkAllUsers"
                     title="Check All Users" @change="checkAllEmployees"/>
              <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="user_id" :options="users" label="fullname" :value="0" :reduce="(user) => user.id"
                        placeholder="Select Employee" class="mt-2" multiple/>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Mapping Types</template>
            <template #input>
              <v-select v-model="mapping_type_id" :options="mappingTypes" label="name" :value="0"
                        :reduce="(type) => type.id" placeholder="Select Mapping Type" class="mt-2"/>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Product Types</template>
            <template #input>
              <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Type" class="mt-2" @input="getProductData"/>
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-8">
          <c-form-group>
            <template #label> Product</template>
            <template #input>
              <input label="All" id="products" v-if="products.length != 0" class="m-1" type="checkbox"
                     v-model="checkAllProducts" title="Check All Products" @change="checkAllProduct"/>
              <label for="products" v-if="products.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="product_id" :options="products" label="name" :value="0"
                        :reduce="(product) => product.id" placeholder="Select Product" class="mt-2" multiple/>
            </template>
          </c-form-group>
        </div>

        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> View <span style="color: red">*</span></template>
            <template #input>
              <v-select v-model="view" :options="['Sales', 'Months']" placeholder="Select View" class="mt-2"/>
            </template>
          </c-form-group>
        </div>
        <!-- <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Sales By </template>
            <template #input>
              <v-select v-model="sales_by" :options="['All', 'Units', 'Values']" placeholder="Select Sales By"
                class="mt-2" />
            </template>
          </c-form-group>
        </div> -->

        <div v-if="filter == 1" style="padding-top: 45px; padding-left: 60px" class="col-lg-2 col-md-2 col-sm-8">
          <strong>Show Bricks : </strong>
          <input class="px-4" type="checkbox" v-model="checked"/>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id.length > 0" color="primary" class="text-white" @click="show" style="float: right">Show
      </c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  props: {
    reportName: {
      type: String,
      required: true
    }
  },
  emits: ["getSchedule"],
  data() {
    return {
      lines: [],
      users: [],
      mappingTypes: [],
      mapping_type_id: null,
      types: [
        {id: 1, name: "Private"},
        {id: 2, name: "Brand"},
        {id: 3, name: "Tender"},
      ],
      type_id: 1,
      divisions: [],
      products: [],
      line_id: [],
      user_id: [],
      sales_by: null,
      division_id: [],
      view: "Sales",
      filters: [
        {id: 1, name: "Division"},
        {id: 2, name: "Employee"},
      ],
      filter: 1,
      checked: 0,
      product_id: [],
      checkAllLines: false,
      checkAllUsers: false,
      checkAllDivisions: false,
      checkAllProducts: false,
      from_date: null,
      to_date: null,
      dateLimit: null
    };
  },
  methods: {
    setDates(date) {
      this.dateLimit = date
      if (!date) {
        this.from_date = moment().startOf("month").format("YYYY-MM-DD")
        this.to_date = moment().endOf("month").format("YYYY-MM-DD")
        return;
      }

      this.from_date = moment(date).startOf("month").format("YYYY-MM-DD")
      this.to_date = moment(date).endOf("month").format("YYYY-MM-DD")

    },
    async getDateLimit() {
      return axios.get("/api/sales/date/limit")
        .then((res) => this.setDates(res.data.limit))
        .catch(this.showErrorMessage);
    },
    async getLineAndMappingTypes() {
      return axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'salesTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.mappingTypes = response.data.data.salesTypes;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    async initialize() {
      return this.getDateLimit()
    },
    getAllData() {
      this.line_id = [];
      this.user_id = [];
      this.division_id = [];
      this.product_id = [];
      this.getLineAndMappingTypes();
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          productType: this.type_id ?? null,
          data: ['divisions', 'products', 'users']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.products = response.data.data.products;
          this.users = response.data.data.users;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getProductData() {
      axios
        .post("/api/get-product-data/", {
          lines: this.line_id,
          type_id: this.type_id,
          from: this.from_date,
          to: this.to_date,
        })
        .then((response) => {
          this.product_id = [];
          this.products = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = [];
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = [];
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = [];
    },
    show() {
      let saleFilter = {
        lines: this.line_id,
        users: this.user_id,
        divisions: this.division_id,
        filter: this.filter,
        products: this.product_id,
        type: this.type_id,
        mappingType: this.mapping_type_id,
        sales_by: this.sales_by,
        checked: this.checked ? 1 : 0,
        view: this.view,
        fromDate: this.from_date,
        toDate: this.to_date,
      };
      this.$emit("getSchedule", {saleFilter});
    },
  },
  created() {
    this.initialize()
      .then(() => this.getLineAndMappingTypes())
  },
};
</script>
