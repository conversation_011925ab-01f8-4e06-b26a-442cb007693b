<template>
  <c-card>
    <c-card-header>Commercial & Branding Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Main
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label>
                      Line <span style="color: red">*</span></template>
                    <template #input>
                      <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox"
                        v-model="checkAllLines" title="Check All lines" @change="checkAllLine" />
                      <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Line" class="mt-2" @input="getLineData" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label>
                      Filter By <span style="color: red">*</span></template>
                    <template #input>
                      <v-select v-model="filter" :options="filters" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Filter" class="mt-2" @input="
                          filter == 1 ? (user_id = []) : (division_id = [])
                          " />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 1">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input label="All" id="division" v-if="divisions.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
                      <label for="division" v-if="divisions.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                        :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="filter == 2">
                  <c-form-group>
                    <template #label> Employee </template>
                    <template #input>
                      <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                        v-model="checkAllUsers" title="Check All Users" @change="checkAllEmployees" />
                      <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="user_id" :options="users" label="fullname" :value="0"
                        :reduce="(user) => user.id" placeholder="Select Employee" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> Approval Types </template>
                    <template #input>
                      <v-select v-model="approval_id" :options="approvals" label="name" :value="0"
                        :reduce="(approval) => approval.value" placeholder="Select Approval Types" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> Payment </template>
                    <template #input>
                      <v-select v-model="payment_id" :options="payments" label="name" :value="0"
                        :reduce="(payment) => payment.value" placeholder="Select Payment Type" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> Archived </template>
                    <template #input>
                      <v-select v-model="archived" :options="['No', 'Yes', 'Total']" placeholder="Select Archived"
                        class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input label="To" type="date" placeholder="To" v-model="to_date"></c-input>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Types & Products
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Products </template>
                    <template #input>
                      <input label="All" v-if="products.length != 0" id="product" class="m-1" type="checkbox"
                        v-model="checkAllProducts" title="Check All Products" @change="checkAllProduct" />
                      <label for="product" v-if="products.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="product_id" :options="products" label="name" :value="0"
                        :reduce="(product) => product.id" placeholder="Select Product" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Request Type </template>
                    <template #input>
                      <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                        v-model="allRequestTypes" title="Check All Types" @change="checkAllRequestType" />
                      <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Commercial Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-input label="Payment From" type="date" placeholder="Payment From"
                    v-model="payment_from_date"></c-input>
                </div>

                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-input label="Payment To" type="date" placeholder="Payment To" v-model="payment_to_date"></c-input>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id != null" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      division_id: [],
      product_id: [],
      type_id: [],
      user_id: [],
      line_id: [],
      divisions: [],
      approvals: [
        { name: "Pending", value: 1 },
        { name: "Approved", value: 2 },
        { name: "Disapproved", value: 3 },
        { name: "Pending & Approved", value: 4 },
        { name: "Approved By Me", value: 5 },
        { name: "Total", value: 6 },
      ],
      approval_id: 6,
      payments: [
        { name: "No", value: 1 },
        { name: "Yes", value: 2 },
        { name: "Partial", value: 3 },
        { name: "Total", value: 4 },
      ],
      payment_id: 4,
      archived: 'Total',
      products: [],
      types: [],
      lines: [],
      users: [],
      filters: [
        { id: 1, name: "Division" },
        { id: 2, name: "Employee" },
      ],
      filter: 2,
      checkAllDivisions: false,
      checkAllLines: false,
      allRequestTypes: true,
      checkAllUsers: false,
      checkAllProducts: false,
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),
      payment_from_date: null,
      payment_to_date: null,
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/commercial-report-lines")
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.requestTypes;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-line-commercial-data`, {
          lines: this.line_id
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.users = response.data.data.users;
          this.products = response.data.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) {
        this.line_id = [];
        this.product_id = [];
        this.user_id = [];
        this.division_id = [];
      }
      this.getLineData();
    },
    checkAllRequestType() {
      if (this.allRequestTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allRequestTypes == false) this.type_id = null;
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    show() {
      let commercialFilter = {
        lines: this.line_id,
        filter: this.filter,
        approval: this.approval_id,
        payment: this.payment_id,
        divisions: this.division_id,
        types: this.type_id,
        products: this.product_id,
        users: this.user_id,
        archived: this.archived,
        fromDate: this.from_date,
        toDate: this.to_date,
        paymentFromDate: this.payment_from_date,
        paymentToDate: this.payment_to_date,
      };
      this.$emit("getSchedule", { commercialFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>