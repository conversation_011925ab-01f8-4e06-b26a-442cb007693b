<template>
  <c-card>
    <c-card-header>Target Summary Report</c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input label="From Date" type="date" class="m-3" placeholder="Date" v-model="from_date"></c-input>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-input label="To Date" type="date" placeholder="Date" class="m-3" v-model="to_date"
            @input="getAllData"></c-input>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Line </template>
            <template #input>
              <input label="All" v-if="lines.length != 0" class="mr-1" id="lines" type="checkbox"
                v-model="checkAllLines" title="Check All Lines" @change="checkAllLine" />
              <label for="lines" v-if="lines.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select option" class="mt-2" @input="getLineData" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Division </template>
            <template #input>
              <input label="All" id="divisons" v-if="divisions.length != 0" class="mr-1" type="checkbox"
                v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivision" />
              <label for="divisions" v-if="divisions.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                :reduce="(division) => division.id" placeholder="Select Division" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label> Product </template>
            <template #input>
              <input label="All" id="products" v-if="products.length != 0" class="m-1" type="checkbox"
                v-model="checkAllProducts" title="Check All Products" @change="checkAllProduct" />
              <label for="products" v-if="products.length != 0" style="font-weight: bold">All</label>
              <v-select v-model="product_id" :options="products" label="name" :value="0"
                :reduce="(product) => product.id" placeholder="Select Product" class="mt-2" multiple />
            </template>
          </c-form-group>
        </div>
        <div class="col-lg-2 col-md-2 col-sm-8">
          <c-form-group>
            <template #label>
              Target By
            </template>
            <template #input>
              <v-select v-model="filter" :options="filters" label="name" :value="0" :reduce="(line) => line.id"
                placeholder="Select Filter" class="mt-3" />
            </template>
          </c-form-group>
        </div>

      </div>
    </c-card-body>
    <c-card-footer>
      <c-button v-if="line_id.length > 0" color="primary" class="text-white" @click="show"
        style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";
export default {
  components: {
    vSelect,
  },
  emits: ["getSchedule"],
  data() {
    return {
      lines: [],
      divisions: [],
      products: [],
      // year: [2024,2023,2022,2021,2020],
      line_id: [],
      division_id: [],
      product_id: [],
      filter: 1,
      filters: [
        { id: 1, name: "All" },
        { id: 2, name: "Units" },
        { id: 3, name: "Values" },
      ],
      year_id: '',
      checkAllLines: false,
      checkAllDivisions: false,
      checkAllProducts: false,
      from_date: moment().startOf('month').format("YYYY-MM-DD"),
      to_date: moment().endOf('month').format("YYYY-MM-DD"),

    };
  },
  methods: {
    getAllData() {
      this.line_id = [];
      this.division_id = [];
      this.product_id = [];
      this.checkAllLines = false;
      this.checkAllDivisions = false;
      this.checkAllProducts = false;
      this.initialize();
    },
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data: ['divisions', 'products']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.products = response.data.data.products;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = [];
      this.getLineData();
    },
    checkAllDivision() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = [];
    },
    checkAllProduct() {
      if (this.checkAllProducts)
        this.product_id = this.products.map((item) => item.id);
      if (this.checkAllProducts == false) this.product_id = [];
    },
    show() {
      let targetFilter = {
        lines: this.line_id,
        divisions: this.division_id,
        products: this.product_id,
        // year: this.year_id
        fromDate: this.from_date,
        toDate: this.to_date,
        filter: this.filter,
        // toDate: this.to_date,
      };
      this.$emit("getSchedule", { targetFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
