<template>
  <div>
    <validation-observer ref="form" v-slot="{ invalid }">
      <CCard no-header>
        <CCardBody>
          <h4 v-if="product" class="pb-3">
            Add Message To:
            <span class="font-weight-bold">{{ product.name }}</span>
          </h4>
          <div class="row">
            <div class="col">
              <CFormGroup>
                <template #label>
                  Message
                </template>
                <template #input>
                  <validation-provider
                    v-slot="{ errors, dirty, valid }"
                    name="message"
                    rules="required"
                  >
                    <v-select
                      v-model="productMessage.message_id"
                      :options="messages"
                      label="message"
                      :value="0"
                      :reduce="message => message.id"
                      :class="!dirty ? '' : valid ? 'is-valid' : 'is-invalid'"
                      placeholder="Select option"
                      multiple
                      class="mt-2"
                    />
                    <div class="invalid-feedback">{{ errors[0] }}</div>
                  </validation-provider>
                </template>
              </CFormGroup>
            </div>
          </div>
        </CCardBody>
        <CCardFooter>
          <button
            class="btn btn-primary rounded"
            :style="`cursor: ${invalid ? 'not-allowed' : ''}`"
            :disabled="invalid"
            @click="create"
          >
            Create
          </button>
          <CButton :to="{name:'products'}" color="default">Cancel</CButton>
        </CCardFooter>
      </CCard>
      <product-messages v-if="product" />
    </validation-observer>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { ValidationProvider, ValidationObserver, extend } from "vee-validate";
import { required } from "vee-validate/dist/rules";
import ProductMessages from './ProductMessages.vue';
extend("required", { ...required, message: "The {_field_} field is required" });

export default {
  components: {
    vSelect,
    ProductMessages,
    ValidationObserver,
    ValidationProvider
    
  },
  data() {
    return {
      productMessage: {}
    };
  },
  computed: {
    ...mapState("product", ["product", "messages"])
  },
  methods: {
    ...mapActions("product", ["getProductMessages"]),
    create() {
      const { id } = this.product;

      axios
        .post(`/api/products/${id}/messages`, {
          ...this.productMessage,
          product_id: id
        })
        .then(() => {
          this.getProductMessages().then(this.reset);
        })
        .catch(() => {
          this.flash("oOOops, something went wrong", "error");
        });
    },
    reset() {
      this.productMessage = {};
      this.$refs.form.reset();
      this.scrollToTop();
      this.flash("Product Message Created successfully.");
    }
  },
};
</script>
