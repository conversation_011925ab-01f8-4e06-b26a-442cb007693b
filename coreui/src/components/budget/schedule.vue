<template>
  <c-card>
    <c-card-header>
      <div class="row">
        <div class="col-md-5"><strong>Budget Setup</strong></div>
        <div class="col-md-2">
          <strong>Budget - {{ year }}</strong>
        </div>
        <div class="col-md-5">
          <c-button color="primary" @click="store()" style="float: right"
            >Save</c-button
          >
        </div>
      </div>
    </c-card-header>
    <c-card-body>
      <div class="budget" style="height: auto">
        <table class="table table-striped">
          <!-- products -->
          <thead>
            <th scope="col"><label class="text-white">Divisions</label></th>
            <th scope="col" v-for="(product, index) in dataCols" :key="index">
              <div style="width: 100px" class="text-center text-white">
                <label>{{ product.name }}</label>
              </div>
            </th>
          </thead>
          <!-- divisions and amounts -->
          <tbody v-if="show">
            <tr v-for="(division, rowIndex) in dataRows" :key="rowIndex">
              <td>
                <label>{{ division.name }}</label>
              </td>
              <td
                style="width: 50px"
                v-for="(product, colIndex) in dataCols"
                :key="colIndex"
              >
                <c-input v-model="percent[rowIndex][colIndex].budget" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="store()"
        >Save</c-button
      >
      <c-button color="default" @click="router.go(-1)">Cancel</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
export default {
  props: {
    dataRows: {
      type: Array,
      required: true,
    },
    dataCols: {
      type: Array,
      required: true,
    },
    setting: {
      type: String,
      required: true,
    },
    subTypes: {
      type: Array,
      default: () => [],
    },
    line: {
      type: Number,
      required: true,
    },
    types: {
      type: Array,
      default: () => [],
    },
    year: {
      type: Number,
      required: true,
    },
    month: {
      type: Object,
      default: () => {},
    },
    budgetTime: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      percent: null,
      id: null,
      division: null,
      product: 0,
      show: false,
      budgets: [],
    };
  },
  methods: {
    initialize() {
      this.percent = new Array(this.dataRows.length);
      if (this.setting == "Product") {
        for (let j = 0; j < this.dataRows.length; j++) {
          this.percent[j] = [];
        }
        for (let i = 0; i < this.dataRows.length; i++) {
          for (let j = 0; j < this.dataCols.length; j++) {
            this.percent[i][j] = {
              id: null,
              line_id: this.line,
              types: this.types,
              subTypes: this.subTypes,
              div_id: this.dataRows[i].id,
              product_id: this.dataCols[j].id,
              budget: null,
            };
          }
        }
      }
      if (this.setting == "Line") {
        for (let j = 0; j < this.dataRows.length; j++) {
          this.percent[j] = [];
        }
        for (let i = 0; i < this.dataRows.length; i++) {
          for (let j = 0; j < this.dataCols.length; j++) {
            this.percent[i][j] = {
              id: null,
              line_id: this.line,
              types: this.types,
              subTypes: this.subTypes,
              div_id: this.dataRows[i].id,
              product_id: null,
              budget: null,
            };
          }
        }
      }
    },
    filterData() {
      return this.percent
        .map((item, i) => {
          return item
            .map((el) => {
              if (el.budget == null) return null;
              return el;
            })
            .filter((item) => item != null);
        })
        .filter((item) => item.length != 0);
    },
    getBudgets() {
      axios
        .post("/api/get-budgets", {
          line: this.line,
          types: this.types,
          subTypes: this.subTypes,
          month: this.month,
          year: this.year,
          budgetTime: this.budgetTime,
          products: this.dataCols,
        })
        .then((response) => {
          this.budgets = response.data.budgets;
          response.data.budgets.forEach((element) => {
            for (let i = 0; i < this.dataRows.length; i++) {
              for (let j = 0; j < this.dataCols.length; j++) {
                if (
                  element.div_id == this.percent[i][j].div_id &&
                  element.product_id === this.percent[i][j].product_id
                ) {
                  this.percent[i][j].id = element.id;
                  this.percent[i][j].budget = element.amount;
                }
              }
            }
          });

          this.show = true;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    store() {
      let percent = this.filterData();
      axios
        .post("/api/budget", {
          percent,
          month: this.month,
          year: this.year,
          budgetTime: this.budgetTime,
          budgetIdsToRemove: this.budgets.map((budget) => budget.id),
        })
        .then((response) => {
          this.reset();
          this.flash("The Budget is Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    reset() {
      this.show = false;
      this.percent = null;
      this.id = null;
      this.division = null;
      this.product = 0;
      this.show = false;
      this.budgets = [];
      this.initialize();
      this.getBudgets();
    },
  },
  created() {
    this.reset();
  },
};
</script>
<style scoped>
.budget {
  overflow-y: auto;
  height: 600px;
}
table {
  display: table;
  white-space: nowrap;
  width: 100%;
}
th {
  opacity: 1;
  position: sticky;
  top: 0;
  background: #2eb85c;
}
.form-group {
  margin-bottom: 0;
}
label {
  font-weight: bolder;
  margin-bottom: 0;
}
</style>