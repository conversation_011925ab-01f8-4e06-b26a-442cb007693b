<template>
  <div class="search-box">
    <button class="btn-search">
      <c-icon name="cil-magnifying-glass" />
    </button>
    <input
      type="text"
      class="input-search"
      v-model="search"
      @change="update"
      placeholder="Type to Search..."
    />
  </div>
</template>
<script>
export default {
  emits: ["search-change"],
  data() {
    return {
      search: null,
    };
  },
  methods: {
    update() {
      this.$emit("search-change", this.search);
    },
  },
};
</script>
<style scoped>
.search-box {
  width: fit-content;
  height: fit-content;
  position: relative;
}
.input-search {
  height: 50px;
  width: 50px;
  border-style: none;
  padding: 10px;
  font-size: 18px;
  letter-spacing: 2px;
  outline: none;
  border-radius: 25px;
  transition: all 0.5s ease-in-out;
  background-color: white;
  padding-right: 40px;
  color: black;
}
.input-search::placeholder {
  color: black;
  font-size: 18px;
  letter-spacing: 2px;
  font-weight: 100;
}
.btn-search {
  width: 50px;
  height: 50px;
  border-style: none;
  font-size: 20px;
  font-weight: bold;
  outline: none;
  cursor: pointer;
  border-radius: 50%;
  position: absolute;
  right: 0px;
  color: black;
  background-color: transparent;
  pointer-events: painted;
}
.btn-search:focus ~ .input-search {
  width: 300px;
  border-radius: 0px;
  background-color: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
  transition: all 500ms cubic-bezier(0, 0.11, 0.35, 2);
}
.input-search:focus {
  width: 300px;
  border-radius: 0px;
  background-color: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
  transition: all 500ms cubic-bezier(0, 0.11, 0.35, 2);
}
</style>
