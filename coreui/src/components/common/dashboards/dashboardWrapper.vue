<template>
  <div>
    <div class="control-section dashboard-default">
      <ejs-dashboardlayout
        id='defaultLayout'
        ref="DashbordInstance"
        :cellSpacing="spacing"
        :allowResizing='true'
        :resizableHandles='resizableHandles'
        :allowFloating="true"
        :columns="6"
        :resizeStart="onResizeStart"
        :resize="onResize"
        :resizeStop="onResizeStop"
      >
        <div v-for="name in getMatrixElements"
             :id="name"
             class="e-panel"
             :data-row="matrix[name].y"
             :data-col="matrix[name].x"
             :data-sizeX="matrix[name].sizeX"
             :data-sizeY="matrix[name].sizeY"
        >
          <span @click="onCloseIconHandler" id="close" class="e-template-icon e-clear-icon"></span>
<!--          <div >-->
            <slot :name="name"></slot>
<!--          </div>-->
        </div>

      </ejs-dashboardlayout>
    </div>
  </div>
</template>

<script>
// Import syncfusion dashboardlayout component from layouts package
import {DashboardLayoutComponent} from "@syncfusion/ej2-vue-layouts";

export default {
  name: "DashboardWrapper",
  components: {
    'ejs-dashboardlayout': DashboardLayoutComponent,
  },
  props: {
    matrix: {
      type: Object,
      required: true
    },
    spacing: {
      type: Array,
      default: () => [10, 10]
    },
    onResizeStart: {
      type: Function,
      default: () => {
      }
    },
    onResizeStop: {
      type: Function,
      default: () => {
      }
    },
    onResize: {
      type: Function,
      default: () => {
      }
    }
  },
  data: () => ({
    resizableHandles: ['e-south-east', 'e-east', 'e-west', 'e-north', 'e-south'],
  }),
  computed: {
    getMatrixElements() {
      return Object.keys(this.matrix)
    },
  },
  methods: {
    onCloseIconHandler: function (event) {
      if (event.target.offsetParent) {
        this.$refs.DashbordInstance.removePanel(event.target.offsetParent.id);
      }
    },
  },
}
</script>
<style>
@import "../../../../node_modules/@syncfusion/ej2-base/styles/material.css";
@import "../../../../node_modules/@syncfusion/ej2-vue-layouts/styles/material.css";

.dashboard-default #defaultLayout.e-dashboardlayout.e-control .e-panel > span#close {
  z-index: 10;
}

.dashboard-default #defaultLayout.e-dashboardlayout.e-control .e-panel:hover > span#close {
  display: block;
}

.dashboard-default #defaultLayout .e-panel .e-panel-container .text-align {
  vertical-align: middle;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
}

.dashboard-default .e-template-icon {
  padding: 10px;
  float: right;
  display: none;
}

.dashboard-default .e-clear-icon::before {
  content: '\e7a7';
  font-size: 12px;
  font-family: 'e-icons';
}

.bootstrap4 .dashboard-default .e-clear-icon::before {
  content: '\e745';
}

.tailwind .dashboard-default .e-clear-icon::before, .tailwind-dark .dashboard-default .e-clear-icon::before {
  content: '\e771';
  font-size: 16px;
}

.bootstrap5 .dashboard-default .e-clear-icon::before, .bootstrap5-dark .dashboard-default .e-clear-icon::before {
  content: '\e7e7';
  font-size: 16px;
}

.dashboard-default .text-align {
  line-height: 160px;
}

.dashboard-default .e-clear-icon {
  position: absolute;
  right: 0;
  cursor: pointer;
}

/* high contrast style */
body.highcontrast .dashboard-default #defaultLayout.e-dashboardlayout.e-control .e-panel {
  background: #000;
}

body.tailwind-dark .e-dashboardlayout.e-control .e-panel {
  border: 1px solid #d7d7d7;
}
</style>
