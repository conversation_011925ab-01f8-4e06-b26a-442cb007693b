<template>
  <v-card>
    <v-app-bar color="secondary">
      <slot name="full-screen"> </slot>
      <v-toolbar-title class="pl-0 text-h6" v-if="article">
        {{ article.name }}
      </v-toolbar-title>
    </v-app-bar>
    <v-card-text>
      <quill-editor
        ref="myQuillEditor"
        v-model="content"
        :options="editorOption"
        @ready="onEditorReady($event)"
      />
    </v-card-text>
  </v-card>
</template>

<script>
import { quillEditor } from "vue-quill-editor";
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
export default {
  components: {
    quillEditor,
  },
  props: {
    article: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      content: null,
      editorOption: {
        modules: {
          toolbar: null,
        },
        placeholder: null,
      },
      readOnly: true,
    };
  },
  methods: {
    initialize() {
      this.editor.enable(!this.readOnly);
      this.editor.setContents(JSON.parse(this.article.content));
    },
    onEditorReady(quill) {
      this.initialize();
    },
  },
  computed: {
    editor() {
      return this.$refs.myQuillEditor.quill;
    },
  },
  updated() {
    this.initialize()
  }
};
</script>

<style >
.ql-editor {
  background-color: white;
  border-radius: 5px;
  border: none;
}
.ql-container {
  height: 100%;
}

.ql-editor p img {
  width: 600px;
  height: 400px;
}
</style>
