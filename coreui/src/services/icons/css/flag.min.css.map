{"version": 3, "sources": ["../scss/flag/flag-icons.scss", "flag.css", "../scss/flag/_core.scss"], "names": [], "mappings": "iBAAA;;;;;;ACQA,iBAAA,cCPE,gBAAA,QACA,oBAAA,IACA,kBAAA,UACA,SAAA,SACA,QAAA,aACA,MAAA,aACA,YAAA,IAKA,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA,4BADF,QACE,iBAAA", "sourcesContent": ["/*!\n * CoreUI Icons - Flag Icons\n * @version v1.0.1\n * @link https://coreui.io/icons/flag/\n * Copyright (c) 2020 creativeLabs <PERSON><PERSON>\n * Licensed under CC0 1.0 Universal\n */\n\n@import \"variables\";\n@import \"functions\";\n@import \"core\";\n", "@charset \"UTF-8\";\n/*!\n * CoreUI Icons - Flag Icons\n * @version v1.0.1\n * @link https://coreui.io/icons/flag/\n * Copyright (c) 2020 creativeLabs <PERSON><PERSON>\n * Licensed under CC0 1.0 Universal\n */\n[class^=\"cif-\"], [class*=\" cif-\"] {\n  background-size: contain;\n  background-position: 50%;\n  background-repeat: no-repeat;\n  position: relative;\n  display: inline-block;\n  width: 1.33333333em;\n  line-height: 1em;\n}\n\n.cif-af {\n  background-image: url(../svg/flag/cif-af.svg);\n}\n\n.cif-al {\n  background-image: url(../svg/flag/cif-al.svg);\n}\n\n.cif-dz {\n  background-image: url(../svg/flag/cif-dz.svg);\n}\n\n.cif-ad {\n  background-image: url(../svg/flag/cif-ad.svg);\n}\n\n.cif-ao {\n  background-image: url(../svg/flag/cif-ao.svg);\n}\n\n.cif-ag {\n  background-image: url(../svg/flag/cif-ag.svg);\n}\n\n.cif-ar {\n  background-image: url(../svg/flag/cif-ar.svg);\n}\n\n.cif-am {\n  background-image: url(../svg/flag/cif-am.svg);\n}\n\n.cif-au {\n  background-image: url(../svg/flag/cif-au.svg);\n}\n\n.cif-at {\n  background-image: url(../svg/flag/cif-at.svg);\n}\n\n.cif-az {\n  background-image: url(../svg/flag/cif-az.svg);\n}\n\n.cif-bs {\n  background-image: url(../svg/flag/cif-bs.svg);\n}\n\n.cif-bh {\n  background-image: url(../svg/flag/cif-bh.svg);\n}\n\n.cif-bd {\n  background-image: url(../svg/flag/cif-bd.svg);\n}\n\n.cif-bb {\n  background-image: url(../svg/flag/cif-bb.svg);\n}\n\n.cif-by {\n  background-image: url(../svg/flag/cif-by.svg);\n}\n\n.cif-be {\n  background-image: url(../svg/flag/cif-be.svg);\n}\n\n.cif-bz {\n  background-image: url(../svg/flag/cif-bz.svg);\n}\n\n.cif-bj {\n  background-image: url(../svg/flag/cif-bj.svg);\n}\n\n.cif-bt {\n  background-image: url(../svg/flag/cif-bt.svg);\n}\n\n.cif-bo {\n  background-image: url(../svg/flag/cif-bo.svg);\n}\n\n.cif-ba {\n  background-image: url(../svg/flag/cif-ba.svg);\n}\n\n.cif-bw {\n  background-image: url(../svg/flag/cif-bw.svg);\n}\n\n.cif-br {\n  background-image: url(../svg/flag/cif-br.svg);\n}\n\n.cif-bn {\n  background-image: url(../svg/flag/cif-bn.svg);\n}\n\n.cif-bg {\n  background-image: url(../svg/flag/cif-bg.svg);\n}\n\n.cif-bf {\n  background-image: url(../svg/flag/cif-bf.svg);\n}\n\n.cif-bi {\n  background-image: url(../svg/flag/cif-bi.svg);\n}\n\n.cif-kh {\n  background-image: url(../svg/flag/cif-kh.svg);\n}\n\n.cif-cm {\n  background-image: url(../svg/flag/cif-cm.svg);\n}\n\n.cif-ca {\n  background-image: url(../svg/flag/cif-ca.svg);\n}\n\n.cif-cv {\n  background-image: url(../svg/flag/cif-cv.svg);\n}\n\n.cif-cf {\n  background-image: url(../svg/flag/cif-cf.svg);\n}\n\n.cif-td {\n  background-image: url(../svg/flag/cif-td.svg);\n}\n\n.cif-cl {\n  background-image: url(../svg/flag/cif-cl.svg);\n}\n\n.cif-cn {\n  background-image: url(../svg/flag/cif-cn.svg);\n}\n\n.cif-co {\n  background-image: url(../svg/flag/cif-co.svg);\n}\n\n.cif-km {\n  background-image: url(../svg/flag/cif-km.svg);\n}\n\n.cif-cg {\n  background-image: url(../svg/flag/cif-cg.svg);\n}\n\n.cif-cd {\n  background-image: url(../svg/flag/cif-cd.svg);\n}\n\n.cif-cr {\n  background-image: url(../svg/flag/cif-cr.svg);\n}\n\n.cif-ci {\n  background-image: url(../svg/flag/cif-ci.svg);\n}\n\n.cif-hr {\n  background-image: url(../svg/flag/cif-hr.svg);\n}\n\n.cif-cu {\n  background-image: url(../svg/flag/cif-cu.svg);\n}\n\n.cif-cy {\n  background-image: url(../svg/flag/cif-cy.svg);\n}\n\n.cif-cz {\n  background-image: url(../svg/flag/cif-cz.svg);\n}\n\n.cif-dk {\n  background-image: url(../svg/flag/cif-dk.svg);\n}\n\n.cif-dj {\n  background-image: url(../svg/flag/cif-dj.svg);\n}\n\n.cif-dm {\n  background-image: url(../svg/flag/cif-dm.svg);\n}\n\n.cif-do {\n  background-image: url(../svg/flag/cif-do.svg);\n}\n\n.cif-ec {\n  background-image: url(../svg/flag/cif-ec.svg);\n}\n\n.cif-eg {\n  background-image: url(../svg/flag/cif-eg.svg);\n}\n\n.cif-sv {\n  background-image: url(../svg/flag/cif-sv.svg);\n}\n\n.cif-gq {\n  background-image: url(../svg/flag/cif-gq.svg);\n}\n\n.cif-er {\n  background-image: url(../svg/flag/cif-er.svg);\n}\n\n.cif-ee {\n  background-image: url(../svg/flag/cif-ee.svg);\n}\n\n.cif-et {\n  background-image: url(../svg/flag/cif-et.svg);\n}\n\n.cif-fj {\n  background-image: url(../svg/flag/cif-fj.svg);\n}\n\n.cif-fi {\n  background-image: url(../svg/flag/cif-fi.svg);\n}\n\n.cif-fr {\n  background-image: url(../svg/flag/cif-fr.svg);\n}\n\n.cif-ga {\n  background-image: url(../svg/flag/cif-ga.svg);\n}\n\n.cif-gm {\n  background-image: url(../svg/flag/cif-gm.svg);\n}\n\n.cif-ge {\n  background-image: url(../svg/flag/cif-ge.svg);\n}\n\n.cif-de {\n  background-image: url(../svg/flag/cif-de.svg);\n}\n\n.cif-gh {\n  background-image: url(../svg/flag/cif-gh.svg);\n}\n\n.cif-gr {\n  background-image: url(../svg/flag/cif-gr.svg);\n}\n\n.cif-gd {\n  background-image: url(../svg/flag/cif-gd.svg);\n}\n\n.cif-gt {\n  background-image: url(../svg/flag/cif-gt.svg);\n}\n\n.cif-gn {\n  background-image: url(../svg/flag/cif-gn.svg);\n}\n\n.cif-gw {\n  background-image: url(../svg/flag/cif-gw.svg);\n}\n\n.cif-gy {\n  background-image: url(../svg/flag/cif-gy.svg);\n}\n\n.cif-hk {\n  background-image: url(../svg/flag/cif-hk.svg);\n}\n\n.cif-ht {\n  background-image: url(../svg/flag/cif-ht.svg);\n}\n\n.cif-va {\n  background-image: url(../svg/flag/cif-va.svg);\n}\n\n.cif-hn {\n  background-image: url(../svg/flag/cif-hn.svg);\n}\n\n.cif-xk {\n  background-image: url(../svg/flag/cif-xk.svg);\n}\n\n.cif-hu {\n  background-image: url(../svg/flag/cif-hu.svg);\n}\n\n.cif-is {\n  background-image: url(../svg/flag/cif-is.svg);\n}\n\n.cif-in {\n  background-image: url(../svg/flag/cif-in.svg);\n}\n\n.cif-id {\n  background-image: url(../svg/flag/cif-id.svg);\n}\n\n.cif-ir {\n  background-image: url(../svg/flag/cif-ir.svg);\n}\n\n.cif-iq {\n  background-image: url(../svg/flag/cif-iq.svg);\n}\n\n.cif-ie {\n  background-image: url(../svg/flag/cif-ie.svg);\n}\n\n.cif-il {\n  background-image: url(../svg/flag/cif-il.svg);\n}\n\n.cif-it {\n  background-image: url(../svg/flag/cif-it.svg);\n}\n\n.cif-jm {\n  background-image: url(../svg/flag/cif-jm.svg);\n}\n\n.cif-jp {\n  background-image: url(../svg/flag/cif-jp.svg);\n}\n\n.cif-jo {\n  background-image: url(../svg/flag/cif-jo.svg);\n}\n\n.cif-kz {\n  background-image: url(../svg/flag/cif-kz.svg);\n}\n\n.cif-ke {\n  background-image: url(../svg/flag/cif-ke.svg);\n}\n\n.cif-ki {\n  background-image: url(../svg/flag/cif-ki.svg);\n}\n\n.cif-kr {\n  background-image: url(../svg/flag/cif-kr.svg);\n}\n\n.cif-kp {\n  background-image: url(../svg/flag/cif-kp.svg);\n}\n\n.cif-kw {\n  background-image: url(../svg/flag/cif-kw.svg);\n}\n\n.cif-kg {\n  background-image: url(../svg/flag/cif-kg.svg);\n}\n\n.cif-la {\n  background-image: url(../svg/flag/cif-la.svg);\n}\n\n.cif-lv {\n  background-image: url(../svg/flag/cif-lv.svg);\n}\n\n.cif-lb {\n  background-image: url(../svg/flag/cif-lb.svg);\n}\n\n.cif-ls {\n  background-image: url(../svg/flag/cif-ls.svg);\n}\n\n.cif-lr {\n  background-image: url(../svg/flag/cif-lr.svg);\n}\n\n.cif-ly {\n  background-image: url(../svg/flag/cif-ly.svg);\n}\n\n.cif-li {\n  background-image: url(../svg/flag/cif-li.svg);\n}\n\n.cif-lt {\n  background-image: url(../svg/flag/cif-lt.svg);\n}\n\n.cif-lu {\n  background-image: url(../svg/flag/cif-lu.svg);\n}\n\n.cif-mk {\n  background-image: url(../svg/flag/cif-mk.svg);\n}\n\n.cif-mg {\n  background-image: url(../svg/flag/cif-mg.svg);\n}\n\n.cif-mw {\n  background-image: url(../svg/flag/cif-mw.svg);\n}\n\n.cif-my {\n  background-image: url(../svg/flag/cif-my.svg);\n}\n\n.cif-mv {\n  background-image: url(../svg/flag/cif-mv.svg);\n}\n\n.cif-ml {\n  background-image: url(../svg/flag/cif-ml.svg);\n}\n\n.cif-mt {\n  background-image: url(../svg/flag/cif-mt.svg);\n}\n\n.cif-mh {\n  background-image: url(../svg/flag/cif-mh.svg);\n}\n\n.cif-mr {\n  background-image: url(../svg/flag/cif-mr.svg);\n}\n\n.cif-mu {\n  background-image: url(../svg/flag/cif-mu.svg);\n}\n\n.cif-mx {\n  background-image: url(../svg/flag/cif-mx.svg);\n}\n\n.cif-fm {\n  background-image: url(../svg/flag/cif-fm.svg);\n}\n\n.cif-md {\n  background-image: url(../svg/flag/cif-md.svg);\n}\n\n.cif-mc {\n  background-image: url(../svg/flag/cif-mc.svg);\n}\n\n.cif-mn {\n  background-image: url(../svg/flag/cif-mn.svg);\n}\n\n.cif-me {\n  background-image: url(../svg/flag/cif-me.svg);\n}\n\n.cif-ma {\n  background-image: url(../svg/flag/cif-ma.svg);\n}\n\n.cif-mz {\n  background-image: url(../svg/flag/cif-mz.svg);\n}\n\n.cif-mm {\n  background-image: url(../svg/flag/cif-mm.svg);\n}\n\n.cif-na {\n  background-image: url(../svg/flag/cif-na.svg);\n}\n\n.cif-nr {\n  background-image: url(../svg/flag/cif-nr.svg);\n}\n\n.cif-np {\n  background-image: url(../svg/flag/cif-np.svg);\n}\n\n.cif-nl {\n  background-image: url(../svg/flag/cif-nl.svg);\n}\n\n.cif-nz {\n  background-image: url(../svg/flag/cif-nz.svg);\n}\n\n.cif-ni {\n  background-image: url(../svg/flag/cif-ni.svg);\n}\n\n.cif-ne {\n  background-image: url(../svg/flag/cif-ne.svg);\n}\n\n.cif-ng {\n  background-image: url(../svg/flag/cif-ng.svg);\n}\n\n.cif-nu {\n  background-image: url(../svg/flag/cif-nu.svg);\n}\n\n.cif-no {\n  background-image: url(../svg/flag/cif-no.svg);\n}\n\n.cif-om {\n  background-image: url(../svg/flag/cif-om.svg);\n}\n\n.cif-pk {\n  background-image: url(../svg/flag/cif-pk.svg);\n}\n\n.cif-pw {\n  background-image: url(../svg/flag/cif-pw.svg);\n}\n\n.cif-pa {\n  background-image: url(../svg/flag/cif-pa.svg);\n}\n\n.cif-pg {\n  background-image: url(../svg/flag/cif-pg.svg);\n}\n\n.cif-py {\n  background-image: url(../svg/flag/cif-py.svg);\n}\n\n.cif-pe {\n  background-image: url(../svg/flag/cif-pe.svg);\n}\n\n.cif-ph {\n  background-image: url(../svg/flag/cif-ph.svg);\n}\n\n.cif-pl {\n  background-image: url(../svg/flag/cif-pl.svg);\n}\n\n.cif-pt {\n  background-image: url(../svg/flag/cif-pt.svg);\n}\n\n.cif-qa {\n  background-image: url(../svg/flag/cif-qa.svg);\n}\n\n.cif-ro {\n  background-image: url(../svg/flag/cif-ro.svg);\n}\n\n.cif-ru {\n  background-image: url(../svg/flag/cif-ru.svg);\n}\n\n.cif-rw {\n  background-image: url(../svg/flag/cif-rw.svg);\n}\n\n.cif-kn {\n  background-image: url(../svg/flag/cif-kn.svg);\n}\n\n.cif-lc {\n  background-image: url(../svg/flag/cif-lc.svg);\n}\n\n.cif-vc {\n  background-image: url(../svg/flag/cif-vc.svg);\n}\n\n.cif-ws {\n  background-image: url(../svg/flag/cif-ws.svg);\n}\n\n.cif-sm {\n  background-image: url(../svg/flag/cif-sm.svg);\n}\n\n.cif-st {\n  background-image: url(../svg/flag/cif-st.svg);\n}\n\n.cif-sa {\n  background-image: url(../svg/flag/cif-sa.svg);\n}\n\n.cif-sn {\n  background-image: url(../svg/flag/cif-sn.svg);\n}\n\n.cif-rs {\n  background-image: url(../svg/flag/cif-rs.svg);\n}\n\n.cif-sc {\n  background-image: url(../svg/flag/cif-sc.svg);\n}\n\n.cif-sl {\n  background-image: url(../svg/flag/cif-sl.svg);\n}\n\n.cif-sg {\n  background-image: url(../svg/flag/cif-sg.svg);\n}\n\n.cif-sk {\n  background-image: url(../svg/flag/cif-sk.svg);\n}\n\n.cif-si {\n  background-image: url(../svg/flag/cif-si.svg);\n}\n\n.cif-sb {\n  background-image: url(../svg/flag/cif-sb.svg);\n}\n\n.cif-so {\n  background-image: url(../svg/flag/cif-so.svg);\n}\n\n.cif-za {\n  background-image: url(../svg/flag/cif-za.svg);\n}\n\n.cif-es {\n  background-image: url(../svg/flag/cif-es.svg);\n}\n\n.cif-lk {\n  background-image: url(../svg/flag/cif-lk.svg);\n}\n\n.cif-sd {\n  background-image: url(../svg/flag/cif-sd.svg);\n}\n\n.cif-ss {\n  background-image: url(../svg/flag/cif-ss.svg);\n}\n\n.cif-sr {\n  background-image: url(../svg/flag/cif-sr.svg);\n}\n\n.cif-sz {\n  background-image: url(../svg/flag/cif-sz.svg);\n}\n\n.cif-se {\n  background-image: url(../svg/flag/cif-se.svg);\n}\n\n.cif-ch {\n  background-image: url(../svg/flag/cif-ch.svg);\n}\n\n.cif-sy {\n  background-image: url(../svg/flag/cif-sy.svg);\n}\n\n.cif-tw {\n  background-image: url(../svg/flag/cif-tw.svg);\n}\n\n.cif-tj {\n  background-image: url(../svg/flag/cif-tj.svg);\n}\n\n.cif-tz {\n  background-image: url(../svg/flag/cif-tz.svg);\n}\n\n.cif-th {\n  background-image: url(../svg/flag/cif-th.svg);\n}\n\n.cif-tl {\n  background-image: url(../svg/flag/cif-tl.svg);\n}\n\n.cif-tg {\n  background-image: url(../svg/flag/cif-tg.svg);\n}\n\n.cif-to {\n  background-image: url(../svg/flag/cif-to.svg);\n}\n\n.cif-tt {\n  background-image: url(../svg/flag/cif-tt.svg);\n}\n\n.cif-tn {\n  background-image: url(../svg/flag/cif-tn.svg);\n}\n\n.cif-tr {\n  background-image: url(../svg/flag/cif-tr.svg);\n}\n\n.cif-tm {\n  background-image: url(../svg/flag/cif-tm.svg);\n}\n\n.cif-tv {\n  background-image: url(../svg/flag/cif-tv.svg);\n}\n\n.cif-ug {\n  background-image: url(../svg/flag/cif-ug.svg);\n}\n\n.cif-ua {\n  background-image: url(../svg/flag/cif-ua.svg);\n}\n\n.cif-ae {\n  background-image: url(../svg/flag/cif-ae.svg);\n}\n\n.cif-gb {\n  background-image: url(../svg/flag/cif-gb.svg);\n}\n\n.cif-us {\n  background-image: url(../svg/flag/cif-us.svg);\n}\n\n.cif-uy {\n  background-image: url(../svg/flag/cif-uy.svg);\n}\n\n.cif-uz {\n  background-image: url(../svg/flag/cif-uz.svg);\n}\n\n.cif-ve {\n  background-image: url(../svg/flag/cif-ve.svg);\n}\n\n.cif-vn {\n  background-image: url(../svg/flag/cif-vn.svg);\n}\n\n.cif-ye {\n  background-image: url(../svg/flag/cif-ye.svg);\n}\n\n.cif-zm {\n  background-image: url(../svg/flag/cif-zm.svg);\n}\n\n.cif-zw {\n  background-image: url(../svg/flag/cif-zw.svg);\n}\n\n/*# sourceMappingURL=flag.css.map */", "[class^=\"#{$prefix}\"], [class*=\" #{$prefix}\"] {\n  background-size: contain;\n  background-position: 50%;\n  background-repeat: no-repeat;\n  position: relative;\n  display: inline-block;\n  width: 1.33333333em;\n  line-height: 1em;\n}\n\n@each $icon, $unicode in $icons {\n  $icon-lower: to-lower-case(#{$icon});\n  .#{$prefix}#{$icon-lower} {\n    background-image: url(../svg/flag/#{$prefix}#{$icon}.svg);\n  }\n}\n"]}