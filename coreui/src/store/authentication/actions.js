export default {
  saveAuthUser({commit}, value) {
    commit("setAccountLock", value.locked === 1);
    commit("setAuthUser", value);
  },
  saveFontSize({commit}, value) {
    commit("setFontSize", value);
  },
  inPersonate({state, commit}, id) {
    return axios.get(`/api/users/${id}/in-personate`)
      .then(res => {
        commit("setOldAuthUser", state.authUser)
        localStorage.setItem("old_api_token", localStorage.getItem('api_token'))
        commit("setAuthUser", res.data.user)
        commit("app/setApiToken", res.data.token, {root: true})
        commit("setImpersonated", !state.isImpersonate);
      })
  },
  rollbackPersonate({state, commit}) {
    const authUser = JSON.parse(localStorage.getItem("OldAuthUser"))
    commit("setAuthUser", authUser)
    commit("app/setApiToken", localStorage.getItem('old_api_token'), {root: true})
    commit("setImpersonated", !state.isImpersonate);
    localStorage.removeItem('old_api_token');
    localStorage.removeItem('OldAuthUser');
  },
  loadPermissions({state, commit}) {
    if (state.permissions.length === 0)
      axios
        .get("/api/users/null/permissions")
        .then(res => {
          let userPermissions = res.data.data.map(permission => permission.name);
          commit("setPermissions", userPermissions);
        })
        .catch(() => {
        });
  },
  removeAuthUser({commit}) {
    commit("setAccountLock", false);
    commit("setAuthUser", null);
  },
  lockAccount({commit}, value = true) {
    commit("setAccountLock", value);
  },
  unlockAccount({commit}, value = false) {
    commit("setAccountLock", value);
  }
};
