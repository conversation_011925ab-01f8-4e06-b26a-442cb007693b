<template>
  <c-row>
    <c-col col="12" lg="12">
      <c-card no-header>
        <c-card-body>
          <c-form>
            <template slot="header">
              Edit Dashboard Setting id: {{ $route.params.id }}
            </template>
            <div class="row">
              <div class="col-6">
                <c-input
                  label="Name"
                  type="text"
                  placeholder="Name"
                  v-model="dashboard_setting.name"
                  disabled="disabled"
                >
                  <template slot="label">
                    Name <span style="color: red">*</span>
                  </template>
                </c-input>
              </div>

              <div class="col-6">
                <c-input
                  label="Key"
                  type="text"
                  placeholder="Key"
                  v-model="dashboard_setting.key"
                  disabled="disabled"
                >
                  <template slot="label">
                    Key <span style="color: red">*</span>
                  </template>
                </c-input>
              </div>
            </div>

            <div class="row">
              <div class="col-6">
                <c-form-group
                  v-if="
                    dashboard_setting.type == 'select' &&
                    dashboard_setting.key == 'appear_all_approval_employees'
                  "
                >
                  <template #label> Value </template>
                  <template #input>
                    <v-select
                      title="Value"
                      v-model="dashboard_setting.value"
                      :options="['Yes', 'No']"
                      placeholder="Select Value"
                      class="mt-2"
                    />
                  </template>
                </c-form-group>
              </div>
              <div class="col-6">
                <c-form-group>
                  <template #label> Type </template>
                  <template #input>
                    <v-select
                      title="Type"
                      v-model="dashboard_setting.type"
                      :options="types"
                      placeholder="Select option"
                      class="mt-2"
                      disabled="disabled"
                    />
                  </template>
                </c-form-group>
              </div>
            </div>
          </c-form>
        </c-card-body>
        <c-card-footer>
          <c-button color="primary" @click="update">Save</c-button>
          <c-button color="default" @click="$router.go(-1)">Cancel</c-button>
        </c-card-footer>
      </c-card>
    </c-col>
  </c-row>
</template>
  
  <script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data: () => {
    return {
      dashboard_setting: {
        name: "",
        key: "",
        value: "",
        type: "text",
      },
      types: [
        "text",
        "number",
        "email",
        "date",
        "textarea",
        "map",
        "select",
        "file",
        "url",
        "checkbox",
      ],
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/dashboard-widget-settings/${this.$route.params.id}`)
        .then((response) => {
          this.dashboard_setting = response.data.data;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/dashboard-widget-settings/${this.$route.params.id}`, {
          name: this.dashboard_setting.name,
          key: this.dashboard_setting.key,
          value: this.dashboard_setting.value,
          type: this.dashboard_setting.type,
        })
        .then((response) => {
          this.flash("Plan Visit Setting Updated Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
  