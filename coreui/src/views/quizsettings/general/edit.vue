<template>
    <c-card v-if="loaded">
      <c-card-header> Quiz Setting </c-card-header>
      <c-card-body>
        <div class="row">
          <div class="col-6">
            <c-input label="Name" type="text" placeholder="Name" v-model="name" disabled></c-input>
          </div>
          <div class="col-6">
            <c-form-group v-if="type='select'">
              <template #label> Value </template>
              <template #input>
                <v-select title="Value" v-model="value" :options="options.values" placeholder="Select option"
                  class="mt-2" />
              </template>
            </c-form-group>
          </div>
        </div>
      </c-card-body>
      <c-card-footer>
        <CButton color="primary" @click="update()" style="">Update</CButton>
        <CButton color="default" :to="{ name: 'Quiz Settings' }">Cancel</CButton>
      </c-card-footer>
    </c-card>
  </template>
  
  <script>
  import vSelect from "vue-select";
  import "vue-select/dist/vue-select.css";
  export default {
    components: {
      vSelect,
    },
    data() {
      return {
        name: null,
        key: null,
        value: null,
        type: null,
        options: null,
        types: ["text", "number", "select", "checkbox", "date"],
        loaded: false,
      };
    },
    methods: {
      async initialize() {
        const res = await axios.get("/api/quiz-settings/" + this.$route.params.id)
  
        this.name = res.data.name;
        this.key = res.data.key;
        this.value = res.data.value;
        this.type = res.data.type;
        if (this.type === 'select') {
          this.options = res.data.options;
          console.log(this.options.values);
        }
      },
      update() {
        axios
          .put("/api/quiz-settings/" + this.$route.params.id, {
            name: this.name,
            key: this.key,
            value: this.value,
            type: this.type,
          })
          .then((response) => {
            this.flash("Quiz Setting Updated Successfully");
            this.value = "";
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      },
    },
    async created() {
      await this.initialize();
      this.loaded = true
    },
  };
  </script>
  