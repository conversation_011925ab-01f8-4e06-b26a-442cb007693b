<template>
  <c-row>
    <c-col md="12">
      <c-card>
        <c-card-header><strong>Support Form</strong></c-card-header>
        <c-card-body>
          <div class="row">
            <div class="col-4">
              <c-form-group>
                <template #label> Support Types </template>
                <template #input>
                  <v-select
                    title="Search for option"
                    v-model="support_type_id"
                    class="mt-2"
                    :options="support_types"
                    label="name"
                    :value="0"
                    :reduce="(support_type) => support_type.id"
                  ></v-select>
                </template>
              </c-form-group>
            </div>
            <div class="col-4">
              <c-input
                label="Title"
                type="text"
                placeholder="Title"
                v-model="title"
                >Title</c-input
              >
            </div>
          </div>
          <div class="row">
            <div class="col">
              <c-textarea
                label="Brief"
                type="text"
                placeholder="Brief"
                v-model="brief"
              ></c-textarea>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <template> <label> Attachments </label></template>
              <br />
              <template>
                <input
                  type="file"
                  id="files"
                  multiple
                  ref="file_input"
                  @change="handleFilesUpload"
                />
                <p
                  style="margin-left: 20px"
                  v-if="files_length != 0"
                  class="custom-file-upload"
                >
                  <b>List of files</b>
                </p>
                <ul style="margin-left: 50px">
                  <li v-for="(file, key) in files" :key="key">
                    {{ file.name }}
                  </li>
                </ul>
              </template>
            </div>
          </div>
        </c-card-body>
        <c-card-footer>
          <c-button
            v-if="checkPermission('create_supports')"
            color="primary"
            style="float: right"
            @click="store()"
            >Send</c-button
          >
          <c-button
            v-if="checkPermission('create_supports')"
            color="primary"
            style="float: left"
            @click="placeVideoCall"
            :disabled="adminIsAvailable"
            >Live Support</c-button
          >
        </c-card-footer>
      </c-card>
    </c-col>
  </c-row>
</template>
<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { mapActions, mapState } from "vuex";
export default {
  name: "Support",
  components: {
    vSelect,
  },
  data() {
    return {
      data: [],
      id: null,
      files: [],
      files_length: 0,
      title: null,
      support_type_id: null,
      brief: null,
      support_types: [],
    };
  },
  computed: {
    ...mapState("authentication", ["authUser"]),
    ...mapState("contacts", ["admins"]),
    ...mapState("app", ["onlineChannel"]),
    adminIsAvailable() {
      return this.admins.length == 0 || this.admins[0].online == false;
    },
  },
  methods: {
    ...mapActions("communication", ["loadCaller"]),
    async initialize() {
      try {
        let supportRes = await axios.get("/api/supports");
        this.support_types = supportRes.data.support_types;
      } catch (error) {
        this.showErrorMessage(error);
      }
    },
    async placeVideoCall() {
      await this.loadCaller(this.authUser);
      this.onlineChannel.emit("start calling", {
        caller: {
          id: this.authUser.id,
          name: this.authUser.name,
        },
        callees: this.admins,
        url: this.uuid(),
      });

      this.$root.$call.init("Incoming Call", this.admins);
    },
    handleFilesUpload() {
      var files = this.$refs.file_input.files;
      this.files_length = files.length;
      var data = new FormData();
      for (var i = 0; i < files.length; i++) {
        data.append("input_name[]", files[i]);
      }
      this.files = data.getAll("input_name[]");
    },
    store() {
      let formData = new FormData();
      formData.append("support_type_id", this.support_type_id);
      formData.append("title", this.title);
      formData.append("brief", this.brief);
      if (this.files.length > 0) {
        for (var i = 0; i < this.files.length; i++) {
          formData.append("files[]", this.files[i]);
        }
      } else {
        formData.append("files[]", "");
      }
      axios
        .post("/api/supports", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((response) => {
          this.flash("Support Created Successfully");
          this.id = response.data.id;
          this.send();
          this.brief = null;
          this.title = null;
          this.support_type_id = null;
          document.getElementById("files").value = "";
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    send() {
      axios
        .get(`/api/send/${this.id}`)
        .then((res) => {
          this.flash("Email sent");
        })
        .catch((err) => this.showErrorMessage(err));
    },
  },
  created() {
    this.initialize();
  },
};
</script>


