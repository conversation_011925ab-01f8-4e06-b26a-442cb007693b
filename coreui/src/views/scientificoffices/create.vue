<template>
  <c-row>
    <c-col>
      <c-card>
        <c-card-header>Create Scientific Office</c-card-header>
        <c-card-body>
          <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-input label="Name" type="text" placeholder="Scientific Office" v-model="name"></c-input>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-8">
              <c-input label="sort" type="number" placeholder="Sort" v-model="sort"></c-input>
            </div>
          </div>

        </c-card-body>
        <c-card-footer>
          <c-button color="primary" @click="store()" style="float: right">Create</c-button>
        </c-card-footer>
      </c-card>
    </c-col>
  </c-row>
</template>
<script>
export default {
  data() {
    return {
      name: null,
      sort: null,
    };
  },
  methods: {
    reset() {
      this.name = null;
      this.sort = null;
    },
    store() {
      axios
        .post("/api/scientific-offices", {
          name: this.name,
          sort: this.sort,
        })
        .then((response) => {
          this.reset();
          this.flash("The Scientific Office Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>