<template>
  <CRow>
    <CCol col="12" xl="12">
      <transition name="slide">
        <CCard>
          <CCardHeader> Visit Feedbacks </CCardHeader>
          <CCardBody>
            <CButton
              color="primary"
              v-if="checkPermission('create_visit_feedbacks')"
              :to="{ name: 'CreateVisitFeedback' }"
              >Create Visit Feedback</CButton
            >
            <CDataTable
              hover
              striped
              sorter
              tableFilter
              footer
              :itemsPerPageSelect="perPageSelect"
              :items="items"
              :fields="fields"
              :items-per-page="100"
              :active-page="1"
              :responsive="true"
              :table-filter="{ external: true, lazy: true }"
              pagination
              thead-top
            >
              <template #notes="{ item }">
                <td v-if="item.notes">{{ item.notes.substring(0, 15) }}...</td>
                <td v-else-if="!item.notes"></td>
              </template>

              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <CButton
                      color="primary"
                      class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('show_single_visit_feedbacks')"
                      :to="{ name: 'VisitFeedback', params: { id: item.id } }"
                      ><CIcon name="cil-magnifying-glass"
                    /></CButton>
                    <CButton
                      color="success"
                      class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('edit_visit_feedbacks')"
                      :to="{
                        name: 'EditVisitFeedback',
                        params: { id: item.id },
                      }"
                      ><i class="cil-pencil"></i><CIcon name="cil-pencil"
                    /></CButton>
                    <c-button
                      color="danger"
                      class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('delete_visit_feedbacks')"
                      @click="
                        $root
                          .$confirm('Delete', 'Do you want to delete this record?', {
                            color: 'red',
                            width: 290,
                            zIndex: 200,
                          })
                          .then((confirmed) => {
                            if (confirmed) {
                              deleteVisitFeedback(item);
                            }
                          })
                      "
                      ><c-icon name="cil-trash"
                    /></c-button>
                  </div>
                </td>
              </template>
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ total }}
                </td>
              </template>
            </CDataTable>
          </CCardBody>
        </CCard>
      </transition>
    </CCol>
  </CRow>
</template>

<script>
export default {  
  data(){
    return {
      items: [],
      fields: ["id", "notes", "sort", "actions"],
    };
  },
  methods: {
    removeVisitFeedback(item) {
        const index=this.items.findIndex(type=>item.id==type.id)
        this.items.splice(index,1)
    },
    deleteVisitFeedback(item) {
      axios
        .delete(`/api/visitfeedbacks/${item.id}`)
        .then((res) => {
          this.removeVisitFeedback(item);
          this.flash("Visit Feedback Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },

    getData() {
      this.get("/api/visitfeedbacks", "visit_feedbacks");
    },
  },
  created() {
    this.getData();
  },
};
</script>
