
<template>
  <c-row>
    <c-col col="12" xl="12">
      <transition name="slide">
        <c-card>
          <c-card-header> Target </c-card-header>
          <c-card-body>
            <c-button color="primary" :to="{ name: 'CreateTarget' }"
              >Create Target</c-button
            >
            <c-dropdown
              placement="bottom-end"
              toggler-text="Tools"
              color="primary"
              class="float-right d-inline-block"
            >
            <c-dropdown-item
              v-if="checkPermission('download_all_templates')"
                @click="templateDownload('targets_template.xlsx')"
                >Template Target</c-dropdown-item
              >
              <c-dropdown-item @click="uploadModal = true"
                >Import Target</c-dropdown-item
              >
            </c-dropdown>
            <c-modal
              title="Upload Target"
              color="success"
              :show.sync="uploadModal"
            >
              <c-input-file
                type="file"
                ref="file"
                id="file"
                name="file_name"
                v-on:change="handleFileUpload"
                placeholder="New file"
              />
              <c-progress
                :value="uploadPercentage"
                color="success"
                animated
                showPercentage
                show-value
                style="height: 15px"
                class="mt-1"
                :max="100"
                v-show="progressBar"
              />
              <template #footer>
                <c-button @click="closeModal" color="danger">Discard</c-button>
                <c-button color="success" @click="upload">Upload</c-button>
              </template>
            </c-modal>
            <c-data-table
              hover
              striped
              sorter
              tableFilter
              footer
              itemsPerPageSelect
              :items="items"
              :fields="fields"
              :items-per-page="1000"
              :active-page="1"
              :responsive="true"
              pagination
              thead-top
            >
            <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ items.length }}
                </td>
              </template>
            </c-data-table>
          </c-card-body>
        </c-card>
      </transition>
    </c-col>
  </c-row>
</template>

<script>
export default {

  data() {
    return {
      items:[],
      fields: ["id",'line','product','quantity','date'],
      uploadPercentage: 0,
      progressBar: false,
      uploadModal: false,
      file: null,
      fileName: null,
    };
  },
  created() {
    this.initialize();
  },
  methods: {
    initialize() {
      axios.get("/api/target")
      .then(response =>{
        this.items = response.data.targets;
      })
      .catch((error) => {
        this.showErrorMessage(error)
      })
    },
    handleFileUpload(files, event) {
      this.file = event.target.files[0];
      this.fileName = event.target.files[0].name;
    },
    openModal() {
      this.uploadModal = true;
    },
    closeModal() {
      this.uploadModal = false;
    },
    upload() {
      this.progressBar = true;
      let formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/import-target", formData);
      document.getElementById("file").value = "";
      this.uploadModal = false;
      this.initialize();
    },
  }
};
</script>
