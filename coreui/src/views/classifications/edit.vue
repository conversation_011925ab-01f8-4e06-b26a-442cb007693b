<template>
  <c-row>
    <c-col col="12" lg="12">
      <c-card no-header>
        <c-card-body>
          <c-form>
            <template slot="header">
              Edit Classification id: {{ $route.params.id }}
            </template>
            <div class="row">
              <div class="col-8">
                <c-input
                  label="Name"
                  type="text"
                  placeholder="Name"
                  v-model="name"
                >
                  <template slot="label">
                    Name <span style="color: red">*</span>
                  </template>
                </c-input>
              </div>
              <div class="col-4">
                <c-input
                  label="Sort"
                  type="number"
                  placeholder="Sort"
                  v-model="sort"
                >
                  <template slot="label">
                    Sort <span style="color: red">*</span>
                  </template>
                </c-input>
              </div>
            </div>

            <div class="row">
              <div class="col">
                <c-textarea
                  label="Notes"
                  type="text"
                  placeholder="Notes"
                  v-model="notes"
                ></c-textarea>
              </div>
            </div>
          </c-form>
        </c-card-body>
        <c-card-footer>
          <c-button color="primary" @click="update">Save</c-button>
          <c-button color="default" :to="{ name: 'classifications' }"
            >Cancel</c-button
          >
        </c-card-footer>
      </c-card>
    </c-col>
  </c-row>
</template>

<script>
export default {
  data() {
    return {
      name: "",
      notes: "",
      sort: "",
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/classifications/${this.$route.params.id}/edit`)
        .then((response)=> {
          this.name = response.data.name;
          this.notes = response.data.notes;
          this.sort = response.data.sort;
        })
        .catch((error)=> {
          this.showErrorMessage(error);
        });
    },
    update() {
      axios
        .put(`/api/classifications/${this.$route.params.id}`, {
          id: this.id,
          name: this.name,
          notes: this.notes,
          sort: this.sort,
        })
        .then((response) => {
          this.flash("Classification Updated Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
