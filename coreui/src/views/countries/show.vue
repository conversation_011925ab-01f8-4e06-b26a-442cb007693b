<template>
  <CRow>
    <CCol col="12" lg="12">
      <CCard>
        <CCardHeader> Country id: {{ $route.params.id }} </CCardHeader>
        <CCardBody>
          <CDataTable striped small fixed :items="items" :fields="fields">
            <template slot="value" slot-scope="data">
              <td>
                <strong>{{ data.item.value }}</strong>
              </td>
            </template>
          </CDataTable>
        </CCardBody>
        <CCardFooter>
          <CButton
            color="primary"
            class="text-white"
            :to="{ name: 'countries' }"
            >Back</CButton
          >
        </CCardFooter>
      </CCard>
    </CCol>
  </CRow>
</template>

<script>
export default {
  name: "Country",
  data: () => {
    return {
      items: [],
      fields: [{ key: "key" }, { key: "value" }],
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/countries/${this.$route.params.id}`)
        .then( (response)=> {
          const items = Object.entries(response.data);
          this.items = items.map(([key, value]) => {
            return { key: key, value: value };
          });
        })
        .catch((error)=> {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
