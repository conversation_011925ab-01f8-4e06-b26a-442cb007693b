<template>
  <c-row>
    <c-col col="12" xl="12">
      <transition name="slide">
        <c-card>
          <c-card-header>Promotional Material
            <c-button style="background-color: blueviolet; color: white; float: right" class="btn-sm mt-2 mr-1"
              :items="items" :to="{ name: 'PrintMaterials' }"><c-icon name="cil-print" /></c-button>
          </c-card-header>
          <c-card-body>
            <c-button color="primary" v-if="checkPermission('create_material')"
              :to="{ name: 'CreatePromotionalMaterial' }">Create Promotional Material</c-button>
            <CDropdown placement="bottom-end" toggler-text="Tools" color="primary" class="float-right d-inline-block">
              <CDropdownItem v-if="checkPermission('show_all_material_stocks')" :to="{ name: 'material-stock' }">
                Material Stock</CDropdownItem>
            </CDropdown>
            <c-data-table hover striped sorter columnFilter footer itemsPerPageSelect :items="items" :fields="fields"
              :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top
              @update:column-filter-value="filterColumn">
              <template slot="cleaner">
                <label class="mfe-2">Filter: </label>
                <c-input v-model="search" placeholder="type string..." type="text" />
              </template>
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ totalData }}
                </td>
              </template>
              <!-- <template #name="{ item }">

              </template> -->
              <template #description="{ item }">
                <td v-if="item.description">
                  <p>
                    {{ item.description.substring(0, 15) }}...
                  </p>
                </td>
                <td v-else-if="!item.description"></td>
              </template>
              <template #products="{ item }">
                <td>
                  <CButton color="dark" variant="outline" square size="sm" @click="showProductDetails(item)">
                    show
                  </CButton>
                </td>
              </template>
              <template #approvals="{ item }">
                <td>
                  <CButton color="warning" variant="outline" square size="sm" @click="showApprovalDetails(item)">
                    show
                  </CButton>
                </td>
              </template>
              <template #status="{ item }">
                <td>
                  <strong v-if="item.status == 1" style="color: green">Approved</strong>
                  <strong v-if="item.status == 0" style="color: red">Dispproved</strong>
                  <strong v-if="item.status == null" style="color: blue">Pending</strong>
                </td>
              </template>
              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <c-button color="primary" class="btn-sm mt-2 mr-1" v-if="checkPermission('show_single_material')"
                      :to="{
                        name: 'ShowPromotionalMaterial',
                        params: { id: item.id },
                      }"><c-icon name="cil-magnifying-glass" /></c-button>
                    <c-button v-if="checkPermission('edit_material')" color="success"
                      class="btn-sm mt-2 mr-1"
                      :to="{ name: 'EditPromotionalMaterial', params: { id: item.id } }"><c-icon name="cil-pencil" />
                    </c-button>
                    <c-button style="background-color: blueviolet; color: white" class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('show_single_material')"
                      :to="{ name: 'PrintMaterial', params: { id: item.id } }"><c-icon name="cil-print" /></c-button>
                    <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('delete_material')" @click="
                      $root
                        .$confirm(
                          'Delete',
                          'Do you want to delete this record?',
                          {
                            color: 'red',
                            width: 290,
                            zIndex: 200,
                          }
                        )
                        .then((confirmed) => {
                          if (confirmed) {
                            deletePromotionalMaterial(item);
                          }
                        })
                      "><c-icon name="cil-trash" /></c-button>
                  </div>
                </td>
              </template>
            </c-data-table>
            <c-pagination v-if="items.length != 0" :activePage.sync="page" @update:activePage="initialize()"
              :pages="total" />
          </c-card-body>
        </c-card>
      </transition>
    </c-col>
  </c-row>
</template>
<script>
export default {
  data() {
    return {
      items: [],
      fields: ["id", "line", "type", "user", "products", "date", "vendor", "amount", "description", "approvals", "status", "actions"],
      collapseDuration: 0,
      page: 1,
      total: 0,
      totalData: 0,
      previousTimeout: null,
      previousTimeoutFilter: null,
      search: null,
    };
  },
  methods: {

    filterColumn(data) {
      this.previousTimeoutFilterClear();
      Object.keys(data).forEach(key => {
        if (!data[key]) {
          delete data[key];
        }
      })
      this.previousTimeoutFilter = setTimeout(() => this.loadAfterFiltering(data), 800)
    },
    loadAfterFiltering(data) {
      axios.post("/api/materials/index?page=" + this.page, {
        columnFilter: data
      })
        .then((res) => {
          this.items = res.data.data.data;
          this.total = res.data.data.last_page;
          this.totalData = res.data.data.total;
          this.items.forEach((item) => {
            item.position = {
              lat: parseFloat(item.lat),
              lng: parseFloat(item.lng),
            };
          });
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    initialize() {
      axios
        .post("/api/materials/index?page=" + this.page, {
          query: this.search,
        })
        .then((res) => {
          this.items = res.data.data.data;
          this.total = res.data.data.last_page;
          this.totalData = res.data.data.total;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    // initialize() {
    //   axios
    //     .get("/api/materials")
    //     .then((response) => {
    //       this.items = response.data.data;
    //     })
    //     .catch((error) => {
    //       this.showErrorMessage(error);
    //     });
    // },
    showApprovalDetails(item) {
      axios
        .post("/api/material-approval-data", item)
        .then((response) => {
          const approvals = response.data.data;
          this.$root.$table("Material Approval", approvals);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    showProductDetails(item) {
      axios
        .post("/api/material-products-data", item)
        .then((response) => {
          const products = response.data.data;
          this.$root.$table("Material Product Details", products);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    removePromotionalMaterial(item) {
      const index = this.items.findIndex((type) => item.id === type.id);
      this.items.splice(index, 1);
    },

    deletePromotionalMaterial(item) {
      axios
        .delete(`/api/materials/${item.id}`)
        .then((res) => {
          this.removePromotionalMaterial(item);
          this.flash("Promotional Material Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    timeoutClear() {
      if (this.previousTimeout) {
        clearTimeout(this.previousTimeout);
        this.previousTimeout = null;
      }
    },
    previousTimeoutFilterClear() {
      if (this.previousTimeoutFilter) {
        clearTimeout(this.previousTimeoutFilter);
        this.previousTimeoutFilter = null;
      }
    }
  },
  created() {
    this.initialize();
  },
  watch: {
    search() {
      this.timeoutClear();
      this.previousTimeout = setTimeout(() => this.initialize(), 500);
    },
  },
};
</script>
