<template>
  <CRow>
    <CCol col="12" lg="12">
      <CCard>
        <CCardHeader> Manufacturer id: {{ $route.params.id }} </CCardHeader>
        <CCardBody>
          <CAlert
            :show.sync="dismissCountDown"
            :color="AlertColor"
            fade
            style="white-space: pre"
            >({{ dismissCountDown }}) {{ message }}</CAlert
          >

          <CDataTable striped small fixed :items="items" :fields="fields">
            <template slot="value" slot-scope="data">
              <strong>{{ data.item.value }}</strong>
            </template>
          </CDataTable>

          <CModal
            :title="`Error ${modalCode}`"
            color="danger"
            :show.sync="dangerModalAlert"
          >
            <p v-if="modalStatusText">{{ modalStatusText }}</p>
            <p v-if="modalMessage">{{ modalMessage }}</p>

            <template #footer>
              <CButton @click="dangerModalAlert = false" color="default"
                >Ok</CButton
              >
            </template>
          </CModal>
        </CCardBody>
        <CCardFooter>
          <CButton color="primary" :to="{ name: 'manufacturers' }"
            >Back</CButton
          >
        </CCardFooter>
      </CCard>
    </CCol>
  </CRow>
</template>

<script>
export default {
  data() {
    return {
      items: [],
      fields: [{ key: "key" }, { key: "value" }],
    };
  },
  methods: {
    initialize() {
      axios
        .get(`/api/manufacturers/${this.$route.params.id}`)
        .then((response) => {
          const items = Object.entries(response.data);
          this.items = items.map(([key, value]) => {
            return { key: key, value: value };
          });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
