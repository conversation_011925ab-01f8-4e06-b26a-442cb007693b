<template>
  <div class="component-container">
    <div class="brick-list-component">
      <ejs-toolbar ref="toolbar" class="e-gridlist" :items="toolBar.dataSource" :clicked="onToolBarChange">
      </ejs-toolbar>
      <ejs-grid id="Grid" ref="grid" height="365" :dataSource="grid.dataSource" :allowFiltering="true"
        :allowPaging="true" :pageSettings="grid.pageSettings" :selectionSettings="grid.selectionOptions"
        :filterSettings="grid.filterOptions" :rowSelected="onRowSelected" :rowDeselected="onRowDeSelected">
        <e-columns>
          <e-column field="name" headerText="Name" width="120" textAlign="Center" :isPrimaryKey='true'></e-column>
        </e-columns>
      </ejs-grid>
    </div>

    <gmap-advanced-map class="map-component" :center="center" :zoom="10" style="width: 100%; height: 500px">
      <gmap-polyline v-for="(polyLinePath, index) in polyLinePaths" :key="`polyPath_${index}`" :path.sync="polyLinePath"
        :options="{ strokeColor: '#099AC9', strokeWeight: 3 }">
      </gmap-polyline>
      <gmap-info-window :options="markerInfo.options" :position="markerInfo.position" :opend="markerInfo.open"
        @closeclick="markerInfo.open = false" v-if="markerInfo.open">
        <div v-html="markerInfo.options.content"></div>
      </gmap-info-window>
      <gmap-advanced-marker v-for="(m, index) in markers" :key="`marker_${index}`" :position="m"
        @click="toggleMarkerInfoWindow(m)" @mouseover="toggleMarkerInfoWindow(m)" @mouseout="markerInfo.open = false"
        :content="getMarkerContent(m)">
        <!-- <gmap-advanced-pin :background="getColor(m)?.background" :borderColor="getColor(m)?.borderColor"
          :glyphColor="getColor(m)?.glyphColor">

        </gmap-advanced-pin> -->
      </gmap-advanced-marker>
    </gmap-advanced-map>
  </div>
</template>

<script>

import {
  GridComponent,
  ColumnsDirective,
  ColumnDirective,
  RowDD,
  Toolbar,
  Selection,
  Filter,
  VirtualScroll,
  Page,
} from "@syncfusion/ej2-vue-grids";
import { ToolbarComponent } from "@syncfusion/ej2-vue-navigations";
import { mapState } from "vuex";

const defaultCenter = { lat: 30.0444, lng: 31.2357 };
const minutesLimit = 15;
let mapping = {};
let timer;
let timerInfo;

export default {
  components: {
    "ejs-grid": GridComponent,
    "e-column": ColumnDirective,
    "e-columns": ColumnsDirective,
    "ejs-toolbar": ToolbarComponent,
  },
  provide: {
    grid: [RowDD, Toolbar, Selection, Filter, VirtualScroll, Page],
  },
  data() {
    return {
      grid: {
        dataSource: [],
        selectionOptions: { type: "Single" },
        filterOptions: { type: "Menu" },
        pageSettings: { pageSize: 200 },
      },
      center: defaultCenter,
      markerInfo: {
        options: {
          content: "",
          pixelOffset: { width: 0, height: -30 },
        },
        position: defaultCenter,
        open: false,
      },
      toolBar: {
        dataSource: [
          {
            text: "Reset",
            cssClass: "save-btn",
            disabled: true,
            id: "reset",
            prefixIcon: "e-icons e-redo",
            tooltipText: "Reset",
          },
        ],
      },
      polyLinePaths: [],
      markers: [],
      selectedContact: null,
      pinColors: [
        {
          background: "#4285f4",
          borderColor: "#0087f5",
          glyphColor: "#0055f5"
        }, {
          background: "#fbbc04",
          borderColor: "#e08600",
          glyphColor: "#b65b00"
        }
      ],
    }
  },
  computed: {
    ...mapState("app", ["vApp", "onlineChannel"]),
  },
  methods: {
    async initialize() {
      try {
        let contactsRes = await axios.get(`/api/contacts`);
        this.grid.dataSource = contactsRes.data.data;
        mapping = this.createIdToIndexMap(this.grid.dataSource);
        this.polyLinePaths = this.getPolylinePaths(this.grid.dataSource);
        this.markers = this.getMarkers(this.grid.dataSource);
        this.setupTracingMachenism();
      } catch (error) {
        this.showErrorMessage(error)
      }
    },
    getMarkerContent(marker) {
      const { hours, minutes } = this.dateDiff(new Date(marker.created_at), new Date());

      const tagStatus = hours == 0 && minutes < minutesLimit;

      const tag = document.createElement('div');
      tag.className = tagStatus ? 'online-tag' : 'offline-tag';
      tag.textContent = this.grid.dataSource[mapping[marker.user_id]].name;
      return tag;
    },
    getColor(marker) {
      const { hours, minutes } = this.dateDiff(new Date(marker.created_at), new Date());

      if (hours == 0 && minutes < minutesLimit) {
        return {
          background: "#4285f4",
          borderColor: "#0087f5",
          glyphColor: "#0055f5"
        }
      } else if (hours >= 1 || minutes >= minutesLimit) {
        return {
          background: "#fbbc04",
          borderColor: "#e08600",
          glyphColor: "#b65b00"
        }
      }

    },
    getPolylinePaths(data) {
      return data.map(({ traces }) => traces)
    },
    getMarkers(data) {
      return data.map(({ traces }) => traces).flat();
    },
    getCenterOfMarkers(markers) {
      return this.markers.reduce(
        (acc, { lat, lng }, index, allValues) => {
          acc.lat += lat;
          acc.lng += lng;
          if (allValues.length - 1 === index) {
            acc.lat = acc.lat / allValues.length;
            acc.lng = acc.lng / allValues.length;
          }
          return acc;
        },
        { lat: 0, lng: 0 }
      );
    },
    setupTracingMachenism() {
      this.onlineChannel.on("user-location-update", (tracing) => {
        const index = mapping[tracing.notifiable_id];
        const dataSource = this.grid.dataSource;
        dataSource[index].traces.push(tracing.data);
        this.grid.dataSource = dataSource;

        if (this.selectedContact && this.selectedContact.id === tracing.notifiable_id) {
          this.selectedContact.traces.push(tracing.data);
          this.markers = this.getMarkers(this.selectedContact.traces);
          this.polyLinePaths = this.getPolylinePaths([this.selectedContact]);
          const location = this.getCenterOfMarkers(this.markers);
          this.center = location.lat === 0 && location.lng === 0 ? defaultCenter : location;
        } else {
          this.markers = this.getMarkers(this.grid.dataSource);
          this.polyLinePaths = this.getPolylinePaths(this.grid.dataSource);
        }
        console.log(this.markers);
        console.log(this.polyLinePaths);
      })
    },
    onRowSelected(args) {
      this.toolBar.dataSource = [{
        ...this.toolBar.dataSource[0],
        disabled: false
      }]
      this.selectedContact = args.data;
      this.polyLinePaths = this.getPolylinePaths([this.selectedContact]);
      this.markers = args.data.traces;
      const location = this.getCenterOfMarkers(this.markers);
      this.center = location.lat === 0 && location.lng === 0 ? defaultCenter : location;
    },
    onRowDeSelected(args) {
      this.selectedContact = null;
      this.markers = [];
      this.polyLinePaths = [];
      this.center = defaultCenter;
    },
    toggleMarkerInfoWindow(marker) {
      const userMarkers = this.grid.dataSource[mapping[marker.user_id]].traces.filter(m => m.user_id === marker.user_id);
      console.log(userMarkers);

      if (!userMarkers.length) return;

      const endMarker = userMarkers[0];
      const startMarker = userMarkers[userMarkers.length - 1];
      const pathInfo = userMarkers.map((m, index) => {
        let label = "";
        if (index === 0) label = " (End)";
        if (index === userMarkers.length - 1) label = " (Start)";
        return `<span>➤ (${m.lat}, ${m.lng})${label}</span>`;
      }).join("<br>");
      console.log(startMarker);
      console.log(endMarker);



      const name = this.grid.dataSource[mapping[marker.user_id]].fullname;

      this.markerInfo.position = { lat: marker.lat, lng: marker.lng };
      clearInterval(timerInfo)
      timerInfo = setInterval(() => {
        const date1 = new Date(marker.created_at);

        const time = this.getHumanReadableTimeElapsed(date1, new Date());

        this.markerInfo.options.content = `
      <div class="m-2" style="display:flex; flex-direction: column;">
        <span style="font-weight: bold;">${name}</span>
        <span style="font-weight: bold;">${time}</span>
        <hr>
        <span style="font-weight: bold; color: green;">Start: (${startMarker.lat}, ${startMarker.lng})</span>
        <span style="font-weight: bold; color: red;">End: (${endMarker.lat}, ${endMarker.lng})</span>
        <hr>
        <span style="font-weight: bold;">Path:</span>
        ${pathInfo}
      </div>
    `;
      }, 1000);

      this.markerInfo.open = !this.markerInfo.open;
    },
    createIdToIndexMap(arr) {
      const idToIndexMap = {};
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (item.id !== undefined) {
          idToIndexMap[item.id] = i;
        }
      }
      return idToIndexMap;
    },
    onToolBarChange() {
      console.log("reset");
      this.toolBar.dataSource = [{
        ...this.toolBar.dataSource[0],
        disabled: true,
      }]
      this.polyLinePaths = this.getPolylinePaths(this.grid.dataSource);
      this.markers = this.getMarkers(this.grid.dataSource);

    }
  },
  mounted() {
    this.initialize()
    timer = setInterval(() => {
      this.grid.dataSource = [...this.grid.dataSource];
    }, 1000 * 60)
  },
  destroyed() {
    clearInterval(timer);
  }
}
</script>
<style>
@import "../../../node_modules/@syncfusion/ej2-base/styles/material.css";
@import "../../../node_modules/@syncfusion/ej2-inputs/styles/material.css";
@import "../../../node_modules/@syncfusion/ej2-buttons/styles/material.css";
@import "../../../node_modules/@syncfusion/ej2-dropdowns/styles/material.css";
@import "../../../node_modules/@syncfusion/ej2-navigations/styles/material.css";
@import "../../../node_modules/@syncfusion/ej2-popups/styles/material.css";
@import "../../../node_modules/@syncfusion/ej2-grids/styles/material.css";
@import "../../../node_modules/@syncfusion/ej2-vue-navigations/styles/material.css";
@import "../../../node_modules/@syncfusion/ej2-vue-inputs/styles/material.css";
@import "../../../node_modules/@syncfusion/ej2-vue-popups/styles/material.css";
@import "../../../node_modules/@syncfusion/ej2-vue-grids/styles/material.css";

.component-container {
  display: flex;
  justify-content: space-between;
}

.brick-list-component {
  flex: 1;
}

.map-component {
  flex: 3;
}

.online-tag {
  background-color: #558B2F;
  outline: 1px solid #678d4c;
  border-radius: 8px;
  color: #fff;
  font-size: 14px;
  padding: 10px 15px;
  position: relative;
}

.offline-tag {
  background-color: #ea4335;
  outline: 1px solid #e4695e;
  border-radius: 8px;
  color: #fff;
  font-size: 14px;
  padding: 10px 15px;
  position: relative;
}

.online-tag:after {
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #678d4c;
  content: "";
  height: 0;
  left: 50%;
  position: absolute;
  top: 100%;
  transform: translate(-50%);
  width: 0;
}

.offline-tag:after {
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #e4695e;
  content: "";
  height: 0;
  left: 50%;
  position: absolute;
  top: 100%;
  transform: translate(-50%);
  width: 0;
}
</style>
