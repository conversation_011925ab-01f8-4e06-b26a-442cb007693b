<template>
  <c-col col="12" lg="12">
    <filter-data @getSchedule="filter" />
    <c-card v-if="items.length != 0">
      <c-card-header>
        <h4 class="text-center">
          PV Report
        </h4>
      </c-card-header>
      <c-card-body>
        <c-data-table ref="content" hover header tableFilter striped sorter footer :items="items" :fields="fields"
          :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top id="print">
          <template slot="thead-top">
            <td style="border-top: none"><strong>Total</strong></td>
            <td style="border-top: none" class="text-xs-right">
              {{ items.length }}
            </td>
          </template>
        </c-data-table>
      </c-card-body>
      <c-card-footer>
        <download @getPrint="print" @getxlsx="download" @getpdf="createPDF" @getcsv="downloadCsv" :fields="fields"
          :data="items" :name="name" />
      </c-card-footer>
    </c-card>
  </c-col>
</template>

<script>
import download from "../../components/download-reports/download.vue";
import filterData from "../../components/reports/pvFilters/filterData.vue";
import { Amiri } from "../../assets/fonts/Amiri-Regular-normal";
import jsPDF from "jspdf";
import "jspdf-autotable";
export default {
  components: {
    download,
    filterData,
  },
  data: () => {
    return {
      items: [],
      fields: [],
      types: [],
      dates: [],
      pvData: {},
      name: "PV Report",
    };
  },
  emits: ["downloaded"],
  methods: {
    showApprovalDetails(item) {
      axios
        .post("/api/vacation-approval-data", item)
        .then((response) => {
          const approvals = response.data.data;
          this.$root.$table("Vacation Approval", approvals);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    print() {
      this.$htmlToPaper("print");
    },
    download() {
      this.downloadStyledExcel(
        this.dates,
        this.items,
        this.fields,
        'PV Report',
        'Employee PVs:'
      );
    },
    downloadCsv() {
      this.downloadXlsxaxios
        .post("/api/vacations-download-excel", { vacations: this.items })
        .then((response) => {
          this.excelData = response.data.data;
          this.downloadXlsx(this.excelData, this.name + ".csv");
          this.$emit("downloaded");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        }); (this.items, this.name + ".csv");
      this.$emit("downloaded");
    },
    createPDF() {
      let pdfName = this.name;
      const columns = this.fields.map((field) => {
        return {
          title: capitalize(field),
          dataKey: field,
        };
      });
      const body = this.items;
      const doc = new jsPDF({ filters: ["ASCIIHexEncode"] });

      doc.addFileToVFS("Amiri-Regular.ttf", Amiri);
      doc.addFont("Amiri-Regular.ttf", "Amiri", "normal");
      doc.setFont("Amiri");
      doc.setFontSize(10);
      doc.autoTable({
        columns,
        body,
        margin: { top: 10 },
        showHead: "firstPage",
        styles: {
          lineColor: "#c7c7c7",
          lineWidth: 0,
          cellPadding: 2,
          font: "Amiri",
        },
      });
      doc.save(pdfName + ".pdf");
    },
    filter({ pvFilter }) {
      this.pvData = pvFilter;
      axios
        .post(`/api/pv-report`, {
          pvFilter,
        })
        .then((response) => {
          this.items = response.data.data;
          this.fields = response.data.fields;
          this.dates = response.data.dates;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>
