<template>
  <CRow>
    <CCol col="12" xl="12">
      <transition name="slide">
        <CCard>
          <CCardHeader> Linked Pharmacies </CCardHeader>
          <CCardBody>
            <CButton
              color="primary"
              v-if="checkPermission('create_linked_pharmacies')"
              :to="{ name: 'linked-pharmacies-to-accounts' }"
              >Create Linked Pharmacies</CButton
            >
            <CDropdown
              placement="bottom-end"
              toggler-text="Tools"
              color="primary"
              class="float-right d-inline-block"
            >
              <CDropdownItem
                v-if="checkPermission('download_all_templates')"
                @click="templateDownload('LinkedPharmacy_Template.xlsx')"
                >Template</CDropdownItem
              >

              <c-dropdown-item
                v-if="checkPermission('import_linked_pharmacies')"
                @click="successModal = true"
                >Upload New</c-dropdown-item
              >

              <!-- <c-dropdown-item
                v-if="checkPermission('import_bulk_edit')"
                @click="updateModal = true"
                >Bulk Edit</c-dropdown-item
              > -->

              <c-dropdown-divider></c-dropdown-divider>

              <CDropdownItem
                v-if="checkPermission('export_xlsx_linked_pharmacies')"
                @click="exportLinkedPharmacy()"
                >Export to Excel</CDropdownItem
              >
              <CDropdownItem
                v-if="checkPermission('export_csv_linked_pharmacies')"
                @click="exportCSV()"
                >Export to CSV</CDropdownItem
              >
              <CDropdownItem
                v-if="checkPermission('export_pdf_linked_pharmacies')"
                @click="exportLinkedPharmacyPDF()"
                >Export to PDF</CDropdownItem
              >
              <CDropdownItem
                v-if="checkPermission('export_email_linked_pharmacies')"
                @click="sendModal = true"
                >Send to Mail</CDropdownItem
              >
            </CDropdown>

            <CModal
              title="Upload Linked Pharmacy"
              color="success"
              :show.sync="successModal"
            >
              <CInputFile
                type="file"
                ref="file"
                id="file"
                name="file_name"
                v-on:change="handleFileUpload"
                placeholder="New file"
              />
              <CProgress
                :value="uploadPercentage"
                color="success"
                animated
                showPercentage
                show-value
                style="height: 15px"
                class="mt-1"
                :max="100"
                v-show="progressBar"
              />
              <template #footer>
                <CButton @click="successModal = false" color="danger"
                  >Discard</CButton
                >
                <CButton
                  @click="importLinkedPharmacy()"
                  color="success"
                  text-color="white"
                  >Upload</CButton
                >
              </template>
            </CModal>
            <!-- bulk edit - update modal -->
            <!-- <c-modal
              title="Bulk Edit Levels"
              color="success"
              :show.sync="updateModal"
            >
              <c-input-file
                type="file"
                ref="file"
                id="bulkEditFile"
                name="file_name"
                v-on:change="handleFileUpload"
                placeholder="New file"
              />
              <c-progress
                :value="uploadPercentage"
                color="success"
                animated
                showPercentage
                show-value
                style="height: 15px"
                class="mt-1"
                :max="100"
                v-show="progressBar"
              />
              <template #footer>
                <c-button @click="updateModal = false" color="danger"
                  >Discard</c-button
                >
                <c-button
                  @click="importUpdateLevel()"
                  class="text-white"
                  color="success"
                  >Upload</c-button
                >
              </template>
            </c-modal> -->

            <CModal title="Compose" color="success" :show.sync="sendModal">
              <template>
                <div class="form-group">
                  <vue-tags-input
                    v-model="email"
                    :tags="emails"
                    name="email[]"
                    :validation="validation"
                    placeholder="To"
                    :add-on-key="addOnKey"
                    @tags-changed="(newEmails) => (emails = newEmails)"
                  />
                </div>
              </template>

              <CTextarea
                type="text"
                name="text"
                v-model="text"
                placeholder="Message"
              ></CTextarea>
              <template #footer>
                <CButton @click="sendModal = false" color="danger"
                  >Discard</CButton
                >
                <CButton @click="sendLinkedPharmacyMail()" color="success"
                  >Send</CButton
                >
              </template>
            </CModal>
            <CDataTable
              hover
              striped
              sorter
              footer
              itemsPerPageSelect
              :items="items"
              :fields="fields"
              :items-per-page="1000"
              :active-page="1"
              :responsive="true"
              pagination
              thead-top
            >
              <template slot="cleaner">
                <label class="mfe-2">Filter: </label>
                <c-input
                  v-model="search"
                  placeholder="type string..."
                  type="text"
                />
                <!-- @change="getData" -->
              </template>
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ totalData }}
                </td>
              </template>

              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <c-button
                      color="primary"
                      class="btn-sm mt-2 mr-1"
                      v-if="checkPermission('show_single_linked_pharmacies')"
                      :to="{ name: 'linked-pharmacy', params: { id: item.id } }"
                      ><CIcon name="cil-magnifying-glass"
                    /></c-button>
                    <c-button
                      color="danger"
                      v-if="checkPermission('delete_linked_pharmacies')"
                      class="btn-sm mt-2 mr-1"
                      @click="
                        $root
                          .$confirm(
                            'Delete',
                            'Do you want to delete this record?',
                            {
                              color: 'red',
                              width: 290,
                              zIndex: 200,
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              deleteLinkedPharmacy(item);
                            }
                          })
                      "
                      ><c-icon name="cil-trash"
                    /></c-button>
                  </div>
                </td>
              </template>
            </CDataTable>
          </CCardBody>
        </CCard>
      </transition>
    </CCol>
  </CRow>
</template>
  
  <script>
import VueTagsInput from "@johmun/vue-tags-input";

export default {
  components: {
    VueTagsInput,
  },

  data: () => {
    return {
      uploadPercentage: 0,
      progressBar: false,
      addOnKey: [13, 32, ":", ";"],
      items: [],
      fields: ["line", "division","user", "account", "pharmacies", "ratio", "actions"],
      successModal: false,
      updateModal: false,
      sendModal: false,
      file: "",
      file_name: "",
      myResult: "",
      text: "",
      validation: [
        {
          classes: "email",
          rule: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/,
        },
      ],
      email: "",
      emails: [],
      page: 1,
      total: 0,
      totalData: 0,
      previousTimeout: null,
      search: null,
    };
  },
  methods: {
    removeLinkedPharmacy(item) {
      const index = this.items.findIndex((type) => item.id == type.id);
      this.items.splice(index, 1);
    },
    deleteLinkedPharmacy(item) {
      axios
        .delete(`/api/linked-pharmacy/${item.id}`)
        .then((res) => {
          this.removeLinkedPharmacy(item);
          this.flash("Linked Pharmacy Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    importLinkedPharmacy() {
      this.progressBar = true;
      const formData = new FormData();
      formData.append("file", this.file);
      this.importFile("/api/import-linked-pharmacy", formData);
      document.getElementById("file").value = "";
      this.successModal = false;
    },

    handleFileUpload(files, event) {
      this.file = event.target.files[0];
      this.file_name = event.target.files[0].name;
    },
    exportLinkedPharmacy() {
      this.exportFile("/api/export-linked-pharmacy", "LinkedPharmacies.xlsx");
    },
    exportCSV() {
      this.exportFile(
        "/api/export-linked-pharmacy-csv",
        "LinkedPharmacies.csv"
      );
    },
    exportLinkedPharmacyPDF() {
      this.exportFile(
        "/api/export-linked-pharmacy-pdf",
        "LinkedPharmacies.pdf"
      );
    },
    sendLinkedPharmacyMail() {
      const formData = {
        emails: JSON.stringify(this.emails),
        text: this.text,
      };
      this.sendMail("/api/send-mail-linked-pharmacy", formData);
      this.successModal = false;
    },
    getData() {
      axios
        .post("/api/linked-pharmacy/index?page=" + this.page, {
          query: this.search,
        })
        .then((res) => {
          this.items = res.data.data.data;
          this.total = res.data.data.last_page;
          this.totalData = res.data.data.total;
        })
        .catch((err) => {
          this.showErrorMessage(err);
        });
    },
    // getData() {
    //   this.get("/api/linked-pharmacy", "data");
    // },
    timeoutClear() {
      if (this.previousTimeout) {
        clearTimeout(this.previousTimeout);
        this.previousTimeout = null;
      }
    },
  },
  created() {
    this.getData();
  },
  watch: {
    search() {
      this.timeoutClear();
      const id = setTimeout(() => this.getData(), 500);
      this.previousTimeout = id;
    },
  },
};
</script>
  