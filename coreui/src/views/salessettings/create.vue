<template>
  <c-card>
    <c-card-header> Sales Mapping Setting </c-card-header>
    <c-card-body>
      <div class="row">
        <div class="col-6">
          <c-input
            label="Name"
            type="text"
            placeholder="Name"
            v-model="name"
          ></c-input>
        </div>
        <div class="col-6">
          <c-input
            label="Key"
            type="text"
            placeholder="Key"
            v-model="key"
          ></c-input>
        </div>
      </div>
      <div class="row">
        <div class="col-6">
          <c-input
            label="Value"
            type="text"
            placeholder="Value"
            v-model="value"
          ></c-input>
        </div>
        <div class="col-6">
          <c-form-group>
            <template #label> Type </template>
            <template #input>
              <v-select
                title="Type"
                v-model="type"
                :options="types"
                placeholder="Select option"
                class="mt-2"
              />
            </template>
          </c-form-group>
        </div>
      </div>
    </c-card-body>
    <c-card-footer>
      <CButton color="primary" @click="store()" style="">Create</CButton>
      <CButton color="default" :to="{ name: 'sales-mapping-settings' }"
        >Cancel</CButton
      >
    </c-card-footer>
  </c-card>
</template>

<script>
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  data() {
    return {
      name: null,
      key: null,
      value: null,
      type: null,
      types: ["text", "number", "select", "checkbox"],
    };
  },
  methods: {
    store() {
      axios
        .post("/api/sales-settings", {
          name: this.name,
          key: this.key,
          value: this.value,
          type: this.type,
        })
        .then((response) => {
          this.flash("Sales Setting Created Successfully");
          this.name = null;
          this.key = null;
          this.value = null;
          this.type = null;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
};
</script>
