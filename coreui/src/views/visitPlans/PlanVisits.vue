<template>
  <CRow>
    <CCol col="12" xl="12">
      <transition name="slide">
        <CCard>
          <CCardHeader>
            <strong>Plan Visits</strong>
          </CCardHeader>
          <CCardBody>
            <CButton color="primary" v-if="checkPermission('create_plan_visits')" :to="{ name: 'CreateVisitPlan' }">
              Create Visit Plan</CButton>
            <CDropdown placement="bottom-end" toggler-text="Tools" color="primary" class="d-inline-block float-right">
              <CDropdownItem @click="exportPlan()">Export to Excel</CDropdownItem>
              <CDropdownItem @click="exportCSV()">Export to CSV</CDropdownItem>
              <CDropdownItem @click="exportPlanPDF()">Export to PDF</CDropdownItem>
              <CDropdownItem @click="sendModal = true">Send to Mail</CDropdownItem>
            </CDropdown>
            <CModal title="Compose" color="success" :show.sync="sendModal">
              <template>
                <div class="form-group">
                  <vue-tags-input v-model="email" :tags="emails" name="email[]" :validation="validation"
                    placeholder="To" :add-on-key="addOnKey" @tags-changed="(newEmails) => (emails = newEmails)" />
                </div>
              </template>
              <CTextarea label="Message:" type="text" name="text" v-model="text" placeholder="Message"></CTextarea>
              <template #footer>
                <CButton class="text-white" @click="sendModal = false" color="danger">Discard</CButton>
                <CButton class="text-white" @click="sendUserMail()" color="success">Send</CButton>
              </template>
            </CModal>
            <br />
            <div>
              <c-button style="float: right;background-color: blueviolet;" class="text-white" @click="getData()">
                <CIcon name="cil-reload" />
              </c-button>
            </div>
            <br /><br />
            <CDataTable hover striped sorter footer itemsPerPageSelect :items="items" :fields="fields"
              :items-per-page="1000" :active-page="1" :responsive="true" pagination thead-top>
              <template slot="cleaner">
                <label class="mfe-2">Filter: </label>
                <c-input v-model="search" placeholder="type string..." type="text" />
                <!-- @change="getData" -->
              </template>
              <template slot="thead-top">
                <td style="border-top: none"><strong>Total</strong></td>
                <td style="border-top: none" class="text-xs-right">
                  {{ totalData }}
                </td>
              </template>
              <template #doctor="{ item }">
                <td>
                  <strong style="color: blue; cursor: pointer"><a
                      @click="$root.$doctor('Doctor Data', item.doctor_id)">{{
                        item.doctor
                      }}</a></strong>
                </td>
              </template>
              <template #account="{ item }">
                <td>
                  <strong style="color: blue; cursor: pointer"><a
                      @click="$root.$account('Account Data', item.account_id)">{{ item.account }}</a></strong>
                </td>
              </template>
              <template #status="{ item }">
                <td>
                  <strong v-if="item.status == 1" style="color: green">Approved</strong>
                  <strong v-if="item.status == 0" style="color: red">Dispproved</strong>
                  <strong v-if="item.status == null" style="color: blue">Pending</strong>
                </td>
              </template>

              <template #actions="{ item }">
                <td>
                  <div class="row justify-content-center">
                    <CButton color="primary" class="btn-sm mt-2 mr-1" v-if="checkPermission('show_single_plan_visits')"
                      :to="{ name: 'ShowPlanVisit', params: { id: item.id } }">
                      <CIcon name="cil-magnifying-glass" />
                    </CButton>

                    <CButton color="success" class="btn-sm mt-2 mr-1" v-if="
                      checkPermission('edit_plan_visits') &&
                      item.status == null &&
                      setting == 'Before Approval' &&
                      item.date > new Date().toISOString().slice(0, 10) + ' 00:00:00' &&
                      !checkPermission('all_permissions')
                    " :to="{
                        name: 'EditPlanVisit',
                        params: { id: item.id },
                      }"><i class="cil-pencil"></i>
                      <CIcon name="cil-pencil" />
                    </CButton>
                    <CButton color="success" class="btn-sm mt-2 mr-1" v-if="
                      checkPermission('edit_plan_visits') &&
                      (item.status == 1 || item.status == null) &&
                      setting == 'After Approval' &&
                      item.date > new Date().toISOString().slice(0, 10) + ' 00:00:00' &&
                      !checkPermission('all_permissions')
                    " :to="{
                        name: 'EditPlanVisit',
                        params: { id: item.id },
                      }"><i class="cil-pencil"></i>
                      <CIcon name="cil-pencil" />
                    </CButton>
                    <CButton color="success" class="btn-sm mt-2 mr-1" v-if="checkPermission('all_permissions')" :to="{
                      name: 'EditPlanVisit',
                      params: { id: item.id },
                    }"><i class="cil-pencil"></i>
                      <CIcon name="cil-pencil" />
                    </CButton>
                    <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="
                      checkPermission('delete_plan_visits') &&
                      item.status == null &&
                      setting == 'Before Approval' &&
                      item.date > new Date().toISOString().slice(0, 10) + ' 00:00:00' &&
                      !checkPermission('all_permissions')
                    " @click="
                        $root
                          .$confirm(
                            'Delete',
                            'Do you want to delete this record?',
                            {
                              color: 'red',
                              width: 290,
                              zIndex: 200,
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              deletePlan(item);
                            }
                          })
                        "><c-icon name="cil-trash" /></c-button>
                    <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="
                      checkPermission('delete_plan_visits') &&
                      (item.status == 1 || item.status == null) &&
                      setting == 'After Approval' &&
                      item.date > new Date().toISOString().slice(0, 10) + ' 00:00:00' &&
                      !checkPermission('all_permissions')
                    " @click="
                        $root
                          .$confirm(
                            'Delete',
                            'Do you want to delete this record?',
                            {
                              color: 'red',
                              width: 290,
                              zIndex: 200,
                            }
                          )
                          .then((confirmed) => {
                            if (confirmed) {
                              deletePlan(item);
                            }
                          })
                        "><c-icon name="cil-trash" /></c-button>
                    <c-button color="danger" class="btn-sm mt-2 mr-1" v-if="checkPermission('all_permissions')" @click="
                      $root
                        .$confirm(
                          'Delete',
                          'Do you want to delete this record?',
                          {
                            color: 'red',
                            width: 290,
                            zIndex: 200,
                          }
                        )
                        .then((confirmed) => {
                          if (confirmed) {
                            deletePlan(item);
                          }
                        })
                      "><c-icon name="cil-trash" /></c-button>
                  </div>
                </td>
              </template>
            </CDataTable>
            <c-pagination v-if="items.length != 0" :activePage.sync="page" @update:activePage="getData()"
              :pages="total" />
          </CCardBody>
        </CCard>
      </transition>
    </CCol>
  </CRow>
</template>

<script>
import VueTagsInput from "@johmun/vue-tags-input";
import { mapState } from "vuex";
export default {
  components: {
    VueTagsInput,
  },
  data() {
    return {
      items: [],
      setting: null,
      successModal: false,
      fields: [
        "id",
        "line",
        "division",
        "user",
        "account",
        "acc_type",
        "doctor",
        "speciality",
        "shift",
        "type",
        "date",
        "start",
        "status",
        "reason",
        "actions",
      ],
      addOnKey: [13, 32, ":", ";"],
      sendModal: false,
      validation: [
        {
          classes: "email",
          rule: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/,
        },
      ],
      email: "",
      emails: [],
      text: "",
      search: null,
      page: 1,
      total: 0,
      totalData: 0,
      previousTimeout: null,
    };
  },
  computed: {
    ...mapState("authentication", ["authUser"]),
  },
  methods: {
    removePlan(item) {
      const index = this.items.findIndex((plan) => item.id == plan.id);
      this.items.splice(index, 1);
    },
    deletePlan(item) {
      axios
        .delete(`/api/plans-daily/${item.id}`)
        .then((res) => {
          this.removePlan(item);
          this.flash("Plan Deleted Successfully");
        })
        .catch((err) => this.showErrorMessage(err));
    },
    exportPlan() {
      this.exportFile("/api/export-plans", "plans.xlsx");
    },
    exportCSV() {
      this.exportFile("/api/export-plans-csv", "plans.csv");
    },
    exportPlanPDF() {
      this.exportFile("/api/export-plans-pdf", "plans.pdf");
    },
    sendUserMail() {
      const formData = {
        emails: JSON.stringify(this.emails),
        text: this.text,
      };
      this.sendMail("/api/send-mail-plans", formData);
      this.successModal = false;
    },
    getData() {
      // this.get("/api/plans", "plan_visits");
      axios
        .post("/api/plans/index?page=" + this.page, {
          query: this.search,
        })
        .then((res) => {
          this.items = res.data.data.plan_visits.data;
          this.setting = res.data.data.setting;
          this.total = res.data.data.plan_visits.last_page;
          this.totalData = res.data.data.plan_visits.to;
        })
        .catch((err) => this.showErrorMessage(err));
    },
    timeoutClear() {
      if (this.previousTimeout) {
        clearTimeout(this.previousTimeout);
        this.previousTimeout = null;
      }
    },
  },
  // created() {
  //   console.log(new Date().toISOString().slice(0, 10) + ' 00:00:00');

  //   this.getData();
  // },
  watch: {
    search() {
      this.timeoutClear();
      const id = setTimeout(() => this.getData(), 500);
      this.previousTimeout = id;
    },
  },
};
</script>
