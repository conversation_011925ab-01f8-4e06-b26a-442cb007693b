// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ProgressBars.vue renders correctly 1`] = `
<div>
  <ccard-stub>
    <ccardheader-stub>
      <cicon-stub
        name="cil-justify-center"
      />
       
      <strong>
         Bootstrap Progress
      </strong>
       
      <div
        class="card-header-actions"
      >
        <a
          class="card-header-action"
          href="https://coreui.io/vue/docs/components/progress"
          rel="noreferrer noopener"
          target="_blank"
        >
          <small
            class="text-muted"
          >
            docs
          </small>
        </a>
      </div>
    </ccardheader-stub>
     
    <ccardbody-stub>
      <cprogress-stub
        animated="true"
        max="100"
        precision="0"
        showpercentage="true"
        value="73"
      />
       
      <cprogress-stub
        class="mt-1"
        max="100"
        precision="0"
        showvalue="true"
        value="0"
      >
        <cprogressbar-stub
          color="gradient-success"
          max="100"
          precision="0"
          value="43.8"
        />
         
        <cprogressbar-stub
          color="gradient-warning"
          max="100"
          precision="0"
          value="18.25"
        />
         
        <cprogressbar-stub
          color="gradient-danger"
          max="100"
          precision="0"
          value="10.95"
        />
      </cprogress-stub>
       
      <cbutton-stub
        activeclass="active"
        class="mt-4"
        color="secondary"
        event="click"
        exactactiveclass="active"
        routertag="a"
        target="_self"
        type="button"
      >
        
        Click me to animate progress bars
      
      </cbutton-stub>
    </ccardbody-stub>
  </ccard-stub>
   
  <ccard-stub>
    <ccardheader-stub>
      <cicon-stub
        name="cil-justify-center"
      />
       
      <strong>
         Progress 
      </strong>
      <small>
        labels
      </small>
    </ccardheader-stub>
     
    <ccardbody-stub>
      <h6>
        No label
      </h6>
       
      <cprogress-stub
        class="mb-3"
        max="50"
        precision="0"
        value="33.333333333"
      />
       
      <h6>
        Value label
      </h6>
       
      <cprogress-stub
        class="mb-3"
        max="50"
        precision="0"
        showvalue="true"
        value="33.333333333"
      />
       
      <h6>
        Progress label
      </h6>
       
      <cprogress-stub
        class="mb-3"
        max="50"
        precision="0"
        showpercentage="true"
        value="33.333333333"
      />
       
      <h6>
        Value label with precision
      </h6>
       
      <cprogress-stub
        class="mb-3"
        max="50"
        precision="2"
        showvalue="true"
        value="33.333333333"
      />
       
      <h6>
        Progress label with precision
      </h6>
       
      <cprogress-stub
        class="mb-3"
        max="50"
        precision="2"
        showpercentage="true"
        value="33.333333333"
      />
    </ccardbody-stub>
  </ccard-stub>
   
  <ccard-stub>
    <ccardheader-stub>
      <cicon-stub
        name="cil-justify-center"
      />
       
      <strong>
         Progress 
      </strong>
       
      <small>
        width
      </small>
    </ccardheader-stub>
     
    <ccardbody-stub>
      <h6>
        Default width
      </h6>
       
      <cprogress-stub
        class="mb-3"
        max="100"
        precision="0"
        value="75"
      />
       
      <h6>
        Custom widths
      </h6>
       
      <cprogress-stub
        class="w-75 mb-2"
        max="100"
        precision="0"
        value="75"
      />
       
      <cprogress-stub
        class="w-50 mb-2"
        max="100"
        precision="0"
        value="75"
      />
       
      <cprogress-stub
        class="w-25"
        max="100"
        precision="0"
        value="75"
      />
    </ccardbody-stub>
  </ccard-stub>
   
  <ccard-stub>
    <ccardheader-stub>
      <cicon-stub
        name="cil-justify-center"
      />
       
      <strong>
         Progress 
      </strong>
       
      <small>
        height
      </small>
    </ccardheader-stub>
     
    <ccardbody-stub>
      <h6>
        Default height
      </h6>
       
      <cprogress-stub
        class="mb-3"
        max="100"
        precision="0"
        showpercentage="true"
        value="75"
      />
       
      <h6>
        Custom heights
      </h6>
       
      <cprogress-stub
        class="mb-2"
        height="2rem"
        max="100"
        precision="0"
        showpercentage="true"
        value="75"
      />
       
      <cprogress-stub
        class="mb-2"
        height="20px"
        max="100"
        precision="0"
        showpercentage="true"
        value="75"
      />
       
      <cprogress-stub
        height="2px"
        max="100"
        precision="0"
        value="75"
      />
    </ccardbody-stub>
  </ccard-stub>
   
  <ccard-stub>
    <ccardheader-stub>
      <cicon-stub
        name="cil-justify-center"
      />
       
      <strong>
         Progress 
      </strong>
       
      <small>
        colors
      </small>
    </ccardheader-stub>
     
    <ccardbody-stub>
      <div
        class="row mb-1"
      >
        <div
          class="col-sm-2"
        >
          gradient-success:
        </div>
         
        <div
          class="col-sm-10 pt-1"
        >
          <cprogress-stub
            color="gradient-success"
            max="100"
            precision="0"
            value="75"
          />
        </div>
      </div>
      <div
        class="row mb-1"
      >
        <div
          class="col-sm-2"
        >
          gradient-info:
        </div>
         
        <div
          class="col-sm-10 pt-1"
        >
          <cprogress-stub
            color="gradient-info"
            max="100"
            precision="0"
            value="75"
          />
        </div>
      </div>
      <div
        class="row mb-1"
      >
        <div
          class="col-sm-2"
        >
          gradient-warning:
        </div>
         
        <div
          class="col-sm-10 pt-1"
        >
          <cprogress-stub
            color="gradient-warning"
            max="100"
            precision="0"
            value="75"
          />
        </div>
      </div>
      <div
        class="row mb-1"
      >
        <div
          class="col-sm-2"
        >
          gradient-danger:
        </div>
         
        <div
          class="col-sm-10 pt-1"
        >
          <cprogress-stub
            color="gradient-danger"
            max="100"
            precision="0"
            value="75"
          />
        </div>
      </div>
      <div
        class="row mb-1"
      >
        <div
          class="col-sm-2"
        >
          gradient-primary:
        </div>
         
        <div
          class="col-sm-10 pt-1"
        >
          <cprogress-stub
            color="gradient-primary"
            max="100"
            precision="0"
            value="75"
          />
        </div>
      </div>
      <div
        class="row mb-1"
      >
        <div
          class="col-sm-2"
        >
          gradient-secondary:
        </div>
         
        <div
          class="col-sm-10 pt-1"
        >
          <cprogress-stub
            color="gradient-secondary"
            max="100"
            precision="0"
            value="75"
          />
        </div>
      </div>
      <div
        class="row mb-1"
      >
        <div
          class="col-sm-2"
        >
          gradient-dark:
        </div>
         
        <div
          class="col-sm-10 pt-1"
        >
          <cprogress-stub
            color="gradient-dark"
            max="100"
            precision="0"
            value="75"
          />
        </div>
      </div>
    </ccardbody-stub>
  </ccard-stub>
   
  <ccard-stub>
    <ccardheader-stub>
      <cicon-stub
        name="cil-justify-center"
      />
       
      <strong>
         Progress 
      </strong>
       
      <small>
        striped
      </small>
    </ccardheader-stub>
     
    <ccardbody-stub>
      <cprogress-stub
        class="mb-2"
        color="success"
        max="100"
        precision="0"
        striped="true"
        value="25"
      />
       
      <cprogress-stub
        class="mb-2"
        color="info"
        max="100"
        precision="0"
        striped="true"
        value="50"
      />
       
      <cprogress-stub
        class="mb-2"
        color="warning"
        max="100"
        precision="0"
        striped="true"
        value="75"
      />
       
      <cprogress-stub
        class="mb-2"
        color="danger"
        max="100"
        precision="0"
        striped="true"
        value="100"
      />
       
      <cbutton-stub
        activeclass="active"
        color="secondary"
        event="click"
        exactactiveclass="active"
        routertag="a"
        target="_self"
        type="button"
      >
        
        Remove Striped
      
      </cbutton-stub>
    </ccardbody-stub>
  </ccard-stub>
   
  <ccard-stub>
    <ccardheader-stub>
      <cicon-stub
        name="cil-justify-center"
      />
       
      <strong>
         Progress 
      </strong>
       
      <small>
        animated
      </small>
    </ccardheader-stub>
     
    <ccardbody-stub>
      <cprogress-stub
        animated="true"
        class="mb-2"
        color="success"
        max="100"
        precision="0"
        striped="true"
        value="25"
      />
       
      <cprogress-stub
        animated="true"
        class="mb-2"
        color="info"
        max="100"
        precision="0"
        striped="true"
        value="50"
      />
       
      <cprogress-stub
        animated="true"
        class="mb-2"
        color="warning"
        max="100"
        precision="0"
        striped="true"
        value="75"
      />
       
      <cprogress-stub
        animated="true"
        class="mb-3"
        color="danger"
        max="100"
        precision="0"
        value="100"
      />
       
      <cbutton-stub
        activeclass="active"
        color="secondary"
        event="click"
        exactactiveclass="active"
        routertag="a"
        target="_self"
        type="button"
      >
        
        Stop Animation
      
      </cbutton-stub>
    </ccardbody-stub>
  </ccard-stub>
   
  <ccard-stub>
    <ccardheader-stub>
      <cicon-stub
        name="cil-justify-center"
      />
       
      <strong>
         Progress 
      </strong>
       
      <small>
        multiple bars
      </small>
    </ccardheader-stub>
     
    <ccardbody-stub>
      <cprogress-stub
        class="mb-3"
        max="100"
        precision="0"
        value="0"
      >
        <cprogressbar-stub
          color="gradient-primary"
          max="100"
          precision="0"
          value="15"
        />
         
        <cprogressbar-stub
          color="gradient-success"
          max="100"
          precision="0"
          value="30"
        />
         
        <cprogressbar-stub
          color="gradient-info"
          max="100"
          precision="0"
          value="20"
        />
      </cprogress-stub>
       
      <cprogress-stub
        class="mb-3"
        max="100"
        precision="0"
        showpercentage="true"
        value="0"
      >
        <cprogressbar-stub
          color="gradient-primary"
          max="100"
          precision="0"
          value="15"
        />
         
        <cprogressbar-stub
          color="gradient-success"
          max="100"
          precision="0"
          value="30"
        />
         
        <cprogressbar-stub
          color="gradient-info"
          max="100"
          precision="0"
          value="20"
        />
      </cprogress-stub>
       
      <cprogress-stub
        class="mb-3"
        max="100"
        precision="0"
        showvalue="true"
        striped="true"
        value="0"
      >
        <cprogressbar-stub
          color="gradient-primary"
          max="100"
          precision="0"
          value="15"
        />
         
        <cprogressbar-stub
          color="gradient-success"
          max="100"
          precision="0"
          value="30"
        />
         
        <cprogressbar-stub
          color="gradient-info"
          max="100"
          precision="0"
          value="20"
        />
      </cprogress-stub>
       
      <cprogress-stub
        class="mb-3"
        max="100"
        precision="0"
        value="0"
      >
        <cprogressbar-stub
          color="gradient-primary"
          max="100"
          precision="0"
          showpercentage="true"
          value="15"
        />
         
        <cprogressbar-stub
          animated="true"
          color="success"
          max="100"
          precision="0"
          showpercentage="true"
          value="30"
        />
         
        <cprogressbar-stub
          color="gradient-info"
          max="100"
          precision="0"
          showpercentage="true"
          striped="true"
          value="20"
        />
      </cprogress-stub>
    </ccardbody-stub>
  </ccard-stub>
</div>
`;
